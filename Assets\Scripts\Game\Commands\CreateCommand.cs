using System;
using System.Collections;
using Game.Managers;
using SmartVertex.Tools;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to create a new object or character.
    /// </summary>
    public class CreateCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the create command.</summary>
        public CreateParams Parameters { get; }

        /// <summary>Creates a new CreateCommand.</summary>
        /// <param name="parameters">Create parameters.</param>
        public CreateCommand(CreateParams parameters)
        {
            Parameters = parameters;
            GridUtility.UseOneBasedIndexing = true;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            switch (Parameters.creationMode)
            {
                case CreationMode.Scene:
                    ObjectManager.Instance.CreateScene(
                        Parameters.sceneID,
                        Parameters.prefabAddress);
                    break;
                case CreationMode.Object:
                    ObjectManager.Instance.CreateObject(
                        Parameters.sceneID,
                        Parameters.objectID,
                        Parameters.prefabAddress,
                        Parameters.gridPosition);
                    break;
                case CreationMode.Character:
                    ObjectManager.Instance.CreateCharacter(
                        Parameters.sceneID,
                        Parameters.objectID,
                        Parameters.prefabAddress,
                        Parameters.gridPosition);
                    break;
                default:
                    Debug.LogError($"Unknown creation mode: {Parameters.creationMode}");
                    break;
            }
            yield break;
        }
    }

    /// <summary>Parameters for Create command.</summary>
    [Serializable]
    public struct CreateParams
    {
        public CreationMode creationMode;
        public string objectID;
        public string prefabAddress;
        public Vector2Int gridPosition;
        public string sceneID;
    }

    public enum CreationMode
    {
        Scene = 0,
        Object = 1,
        Character = 2
    }
}
