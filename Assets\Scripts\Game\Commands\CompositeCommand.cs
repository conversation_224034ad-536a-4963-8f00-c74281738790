using System.Collections;
using System.Collections.Generic;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command that executes a set of child commands in parallel.
    /// </summary>
    public class CompositeCommand : ICoroutineCommand
    {
        /// <summary>Child commands to execute.</summary>
        public List<ICoroutineCommand> Commands { get; }

        /// <summary>Creates a new CompositeCommand.</summary>
        /// <param name="commands">Child commands.</param>
        public CompositeCommand(List<ICoroutineCommand> commands)
        {
            Commands = commands;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            var enumerators = new List<IEnumerator>();
            foreach (var command in Commands)
            {
                enumerators.Add(command.Execute());
            }
            while (enumerators.Count > 0)
            {
                for (int i = enumerators.Count - 1; i >= 0; i--)
                {
                    if (!enumerators[i].MoveNext())
                    {
                        enumerators.RemoveAt(i);
                    }
                    else
                    {
                        yield return enumerators[i].Current;
                    }
                }
                // Yield once per frame to allow all coroutines to progress
                yield return null;
            }
        }
    }
}
