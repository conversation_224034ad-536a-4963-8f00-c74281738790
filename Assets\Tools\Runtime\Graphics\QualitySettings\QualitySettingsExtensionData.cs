using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// ScriptableObject for storing extended quality settings.
    /// </summary>
    [CreateAssetMenu(fileName = "QualitySettingsExtensionData", menuName = "Scriptable Objects/Quality/Quality Settings Extension Data")]
    public class QualitySettingsExtensionData : ScriptableObject
    {
        [Tooltip("Quality index for this extension profile")]
        public QualityIndex qualityIndex = QualityIndex.Low;
        [Tooltip("Max camera rendering distance in units (e.g., meters)")]
        public float maxCameraDistance = 400f;
        [Tooltip("Time step for physics simulation (fixedDeltaTime)")]
        public float fixedDeltaTime = 0.01f;
        [Tooltip("Solver velocity iterations for physics simulation")]
        public int solverVelocityIterations = 1;
        [Tooltip("Solver iterations for physics simulation")]
        public int solverIterations = 6;
        [Tooltip("Target fps for the game")]
        public int fps = 60;
    }
}