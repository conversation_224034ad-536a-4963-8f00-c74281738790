using System;
using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Enum defining the update mode for transform animations
    /// </summary>
    public enum TransformUpdateMode
    {
        /// <summary>
        /// Animation will run in the Update method
        /// </summary>
        Update,

        /// <summary>
        /// Animation will run in the FixedUpdate method
        /// </summary>
        FixedUpdate,

        /// <summary>
        /// Animation will run in the LateUpdate method
        /// </summary>
        LateUpdate
    }

    /// <summary>
    /// Enum defining the transform property to animate
    /// </summary>
    public enum TransformProperty
    {
        /// <summary>
        /// Animate the position of the transform
        /// </summary>
        Position,

        /// <summary>
        /// Animate the rotation of the transform (stored as Euler angles but interpolated using Quaternions)
        /// </summary>
        Rotation,

        /// <summary>
        /// Animate the scale of the transform
        /// </summary>
        Scale
    }

    /// <summary>
    /// Enum defining how the target value should be applied
    /// </summary>
    public enum ValueApplicationMode
    {
        /// <summary>
        /// Set the transform property to the absolute target value
        /// </summary>
        Absolute,

        /// <summary>
        /// Add the target value to the current transform value
        /// </summary>
        Incremental
    }

    /// <summary>
    /// Defines a single transform animation mode (position, rotation, or scale)
    /// </summary>
    [Serializable]
    public class TransformAnimationMode
    {
        /// <summary>
        /// The transform property to animate
        /// </summary>
        [Tooltip("The transform property to animate")]
        public TransformProperty property;

        /// <summary>
        /// Whether to use local or global space
        /// </summary>
        [Tooltip("Whether to use local or global space")]
        public bool useLocalSpace = true;

        /// <summary>
        /// How to apply the target value
        /// </summary>
        [Tooltip("Whether to set the value absolutely or add it incrementally")]
        public ValueApplicationMode applicationMode = ValueApplicationMode.Absolute;

        /// <summary>
        /// The target value for the animation
        /// </summary>
        [Tooltip("The target value for the animation")]
        public Vector3 targetValue;
    }

    /// <summary>
    /// Defines a step in the animation sequence that can contain multiple animation modes
    /// </summary>
    [Serializable]
    public class TransformAnimationStep
    {
        /// <summary>
        /// Array of animation modes to play simultaneously in this step
        /// </summary>
        [Tooltip("Animation modes to play simultaneously")]
        public TransformAnimationMode[] animationModes;

        /// <summary>
        /// Duration of this animation step in seconds
        /// </summary>
        [Tooltip("Duration of this animation step in seconds")]
        public float duration = 1f;

        /// <summary>
        /// Animation curve to control the interpolation
        /// </summary>
        [Tooltip("Animation curve to control the interpolation")]
        public AnimationCurve animationCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
    }

    /// <summary>
    /// ScriptableObject that defines a complete transform animation sequence
    /// </summary>
    [CreateAssetMenu(fileName = "TransformAnimatorData", menuName = "Scriptable Objects/Transform Animator/Transform Animator Data")]
    public class TransformAnimatorData : ScriptableObject
    {

        /// <summary>
        /// The update mode to use for this animation
        /// </summary>
        [Tooltip("The update mode to use for this animation")]
        public TransformUpdateMode updateMode = TransformUpdateMode.Update;

        /// <summary>
        /// Whether to loop the animation
        /// </summary>
        [Tooltip("Whether to loop the animation")]
        public bool loop = false;

        /// <summary>
        /// Whether to play the animation when the GameObject is enabled
        /// </summary>
        [Tooltip("Whether to play the animation when the GameObject is enabled")]
        public bool playOnEnable = false;

        /// <summary>
        /// Array of animation steps to play sequentially
        /// </summary>
        [Tooltip("Animation steps to play sequentially")]
        public TransformAnimationStep[] animationSteps;
    }
}
