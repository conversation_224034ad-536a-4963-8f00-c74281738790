using Game.CommandSystem;
using SmartVertex.Tools;
using UnityEngine;
using UnityEngine.Events;
using System;
using Unity.Cinemachine;
using System.Collections;
using Game.Interfaces;

namespace Game.Managers
{
    /// <summary>
    /// Manages camera behavior and transitions in the game.
    /// </summary>
    public class CameraManager : Singleton<CameraManager>
    {
        /// <summary>
        /// Maps focus speed settings to damping values.
        /// </summary>
        [Serializable]
        public class FocusSpeedMapping
        {
            [Tooltip("The focus speed setting")]
            public FocusSpeed speed;

            [Tooltip("The damping value for this speed")]
            public float damping;
        }

        /// <summary>
        /// Maps shake types to amplitude and frequency values.
        /// </summary>
        [Serializable]
        public class ShakeTypeMapping
        {
            [Tooltip("The type of camera shake")]
            public ShakeType shakeType;

            [Tooltip("The amplitude of the shake")]
            public float amplitude;

            [Tooltip("The frequency of the shake")]
            public float frequency;
        }

        /// <summary>
        /// Maps focus distances to camera offset vectors.
        /// </summary>
        [Serializable]
        public class FocusDistanceMapping
        {
            [Tooltip("The focus distance setting")]
            public FocusDistance distance;

            [Tooltip("The camera offset vector for this distance")]
            public Vector3 offset;
        }

        [SerializeField]
        [Tooltip("Mappings for different focus speed settings")]
        private FocusSpeedMapping[] focusSpeedMappings;

        [SerializeField]
        [Tooltip("Mappings for different camera shake types")]
        private ShakeTypeMapping[] shakeTypeMappings;

        [SerializeField]
        [Tooltip("Mappings for different focus distance settings")]
        private FocusDistanceMapping[] focusDistanceMappings;

        [SerializeField]
        [Tooltip("The main Cinemachine camera")]
        private CinemachineCamera cinemachineCamera;

        private CinemachineHardLockToTarget hardLockToTarget;
        private CinemachineBasicMultiChannelPerlin noise;
        private CinemachineCameraOffset offset;

        /// <inheritdoc/>
        protected override void Awake()
        {
            base.Awake();

            if (cinemachineCamera == null)
            {
                Debug.LogError($"[{nameof(CameraManager)}] CinemachineCamera reference is missing!");
                return;
            }

            hardLockToTarget = cinemachineCamera.GetComponent<CinemachineHardLockToTarget>();
            noise = cinemachineCamera.GetComponent<CinemachineBasicMultiChannelPerlin>();
            offset = cinemachineCamera.GetComponent<CinemachineCameraOffset>();
        }

        /// <summary>
        /// Sets the camera focus transition speed.
        /// </summary>
        /// <param name="speed">The desired focus speed setting.</param>
        public void SetFocusSpeed(FocusSpeed speed)
        {
            var mapping = Array.Find(focusSpeedMappings, m => m.speed == speed);
            if (mapping != null)
            {
                hardLockToTarget.Damping = mapping.damping;
            }
            else
            {
                Debug.LogWarning($"[{nameof(CameraManager)}] Focus speed mapping for {speed} not found.");
            }
        }

        /// <summary>
        /// Sets the camera shake parameters.
        /// </summary>
        /// <param name="shakeType">The desired shake type setting.</param>
        public void SetShakeType(ShakeType shakeType)
        {
            var mapping = Array.Find(shakeTypeMappings, m => m.shakeType == shakeType);
            if (mapping != null)
            {
                noise.AmplitudeGain = mapping.amplitude;
                noise.FrequencyGain = mapping.frequency;
            }
            else
            {
                Debug.LogWarning($"[{nameof(CameraManager)}] Shake type mapping for {shakeType} not found.");
            }
        }

        /// <summary>
        /// Sets the camera distance from the target.
        /// </summary>
        /// <param name="distance">The desired focus distance setting.</param>
        public void SetFocusDistance(FocusDistance distance)
        {
            var mapping = Array.Find(focusDistanceMappings, m => m.distance == distance);
            if (mapping != null)
            {
                offset.Offset = mapping.offset;
            }
            else
            {
                Debug.LogWarning($"[{nameof(CameraManager)}] Focus distance mapping for {distance} not found.");
            }
        }

        /// <summary>
        /// Initiates a camera focus operation on a specified target.
        /// </summary>
        /// <param name="targetId">The ID of the target object to focus on.</param>
        /// <param name="speed">The speed of the focus transition.</param>
        /// <param name="shakeType">The type of camera shake to apply.</param>
        /// <param name="distance">The distance to maintain from the target.</param>
        /// <param name="onComplete">Optional callback when focus completes.</param>
        /// <returns>An IEnumerator for coroutine execution.</returns>
        public IEnumerator FocusOnTarget(
            string targetId,
            FocusSpeed speed = FocusSpeed.Instant,
            ShakeType shakeType = ShakeType.Light,
            FocusDistance distance = FocusDistance.Medium,
            UnityAction onComplete = null)
        {
            if (string.IsNullOrEmpty(targetId))
            {
                Debug.LogError($"[{nameof(CameraManager)}] Target ID cannot be null or empty.");
                yield break;
            }

            SetFocusSpeed(speed);
            SetShakeType(shakeType);
            SetFocusDistance(distance);

            var target = ObjectManager.Instance.FindById(targetId);
            if (target == null)
            {
                Debug.LogWarning($"[{nameof(CameraManager)}] Target with ID {targetId} not found.");
                yield break;
            }

            var focusable = target.GetComponentInChildren<IFocusable>();
            if (focusable == null)
            {
                Debug.LogWarning($"[{nameof(CameraManager)}] Target {targetId} does not implement IFocusable.");
                yield break;
            }

            var cameraTarget = new CameraTarget
            {
                TrackingTarget = focusable.FocusTransform
            };
            cinemachineCamera.Target = cameraTarget;

            yield return new WaitForSeconds(hardLockToTarget.Damping);

            onComplete?.Invoke();
        }
    }
}