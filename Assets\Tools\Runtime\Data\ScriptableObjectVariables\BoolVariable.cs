using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// A ScriptableObject variable that holds a boolean value.
    /// </summary>
    [CreateAssetMenu(fileName = "BoolVariable", menuName = "Scriptable Objects/Variables/Bool Variable")]
    public sealed class BoolVariable : BaseVariable<bool>
    {
        /// <summary>
        /// Logical NOT operator
        /// </summary>
        public static bool operator !(BoolVariable variable) => !variable.value;

        /// <summary>
        /// Logical AND operator
        /// </summary>
        public static bool operator &(BoolVariable a, BoolVariable b) => a.value & b.value;

        /// <summary>
        /// Logical OR operator
        /// </summary>
        public static bool operator |(BoolVariable a, BoolVariable b) => a.value | b.value;

        /// <summary>
        /// Implicitly convert a BoolVariable to a bool
        /// </summary>
        public static implicit operator bool(BoolVariable variable) => variable.value;

        /// <summary>
        /// Toggle the boolean value
        /// </summary>
        public void Toggle()
        {
            Value = !value;
        }
    }
}