using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Generic Singleton implementation for MonoBehaviours.
    /// Ensures only one instance of the component exists in the scene.
    /// </summary>
    /// <typeparam name="T">The type of the MonoBehaviour that will be a Singleton</typeparam>
    public abstract class Singleton<T> : MonoBehaviour where T : MonoBehaviour
    {
        /// <summary>
        /// The static reference to the instance
        /// </summary>
        protected static T instance;

        /// <summary>
        /// Whether this singleton should persist between scene loads
        /// </summary>
        [Tooltip("If true, this singleton will not be destroyed when loading a new scene")]
        [SerializeField] protected bool persistAcrossScenes = false;

        /// <summary>
        /// Gets or sets whether this singleton should persist between scene loads.
        /// When set, automatically applies or removes DontDestroyOnLoad behavior.
        /// </summary>
        public bool PersistAcrossScenes
        {
            get => persistAcrossScenes;
            set
            {
                if (persistAcrossScenes != value)
                {
                    persistAcrossScenes = value;
                    ApplyPersistenceBehavior();
                }
            }
        }

        /// <summary>
        /// Gets the instance of this singleton.
        /// If no instance exists, finds it in the scene.
        /// </summary>
        public static T Instance
        {
            get
            {
                if (instance == null)
                {
                    // Find an existing instance in the scene (faster than FindFirstObjectByType)
                    instance = FindAnyObjectByType<T>();

                    // Log a warning if no instance exists
                    if (instance == null)
                    {
                        Debug.LogWarning($"No instance of {typeof(T).Name} found in scene.");
                    }
                }

                return instance;
            }
        }

        /// <summary>
        /// Called when the script instance is being loaded
        /// </summary>
        protected virtual void Awake()
        {
            if (instance == null)
            {
                // This is the first instance, make it the singleton
                instance = this as T;

                // Apply persistence behavior based on current setting
                ApplyPersistenceBehavior();
            }
            else if (instance != this)
            {
                // Another instance exists, destroy this one
                Debug.LogWarning($"Another instance of {typeof(T).Name} already exists! Destroying this instance.");
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Called when the MonoBehaviour will be destroyed
        /// </summary>
        protected virtual void OnDestroy()
        {
            // If this is the singleton instance, clear the static reference
            if (instance == this)
            {
                instance = null;
            }
        }

        /// <summary>
        /// Called when values are changed in the Inspector (Editor only)
        /// </summary>
        protected virtual void OnValidate()
        {
            // Only apply persistence behavior if this is the singleton instance and we're in play mode
            if (Application.isPlaying && instance == this)
            {
                ApplyPersistenceBehavior();
            }
        }

        /// <summary>
        /// Applies or removes the DontDestroyOnLoad behavior based on the current persistAcrossScenes value
        /// </summary>
        protected virtual void ApplyPersistenceBehavior()
        {
            // Only apply if this is the singleton instance and we're in play mode
            if (instance == this && Application.isPlaying)
            {
                if (persistAcrossScenes)
                {
                    // Apply DontDestroyOnLoad if not already applied
                    if (gameObject.scene.name != "DontDestroyOnLoad")
                    {
                        DontDestroyOnLoad(gameObject);
                    }
                }
                else
                {
                    // Remove DontDestroyOnLoad behavior by moving back to the active scene
                    if (gameObject.scene.name == "DontDestroyOnLoad")
                    {
                        UnityEngine.SceneManagement.SceneManager.MoveGameObjectToScene(gameObject, UnityEngine.SceneManagement.SceneManager.GetActiveScene());
                    }
                }
            }
        }
    }
}