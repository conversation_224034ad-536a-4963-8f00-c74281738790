using System;
using System.Collections.Generic;
using System.Reflection;
using SmartVertex.Tools;
using UnityEditor;
using UnityEngine;

namespace SmartVertex.EditorTools
{
    /// <summary>
    /// Editor window for debugging and inspecting services registered with the ServiceLocator.
    /// </summary>
    public class ServiceLocatorWindow : BaseDebugWindow
    {
        private bool showDetails = true;
        private readonly Dictionary<Type, bool> foldoutStates = new();

        /// <summary>
        /// Shows the Service Locator window.
        /// </summary>
        public static void ShowWindow()
        {
            var window = GetWindow<ServiceLocatorWindow>("Service Locator");
            window.minSize = new Vector2(400, 300);
        }

        /// <inheritdoc/>
        protected override void OnEnable()
        {
            base.OnEnable();
            refreshIcon.tooltip = "Refresh service list";
            clearIcon.tooltip = "Clear all services";
        }

        private void OnGUI()
        {
            InitializeStyles();
            EditorGUILayout.BeginVertical();
            // Header with custom controls
            EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
            GUILayout.Label("Service Locator Debugger", headerStyle);
            GUILayout.FlexibleSpace();
            showDetails = GUILayout.Toggle(showDetails, "Show Details", EditorStyles.toolbarButton);
            if (GUILayout.Button(refreshIcon, EditorStyles.toolbarButton))
            {
                OnRefreshClicked();
            }
            if (GUILayout.Button(clearIcon, EditorStyles.toolbarButton))
            {
                OnClearClicked();
            }
            EditorGUILayout.EndHorizontal();
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            DisplayServices();
            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();
        }

        /// <inheritdoc/>
        protected override void OnRefreshClicked()
        {
            foldoutStates.Clear();
            Repaint();
        }

        /// <inheritdoc/>
        protected override void OnClearClicked()
        {
            if (DisplayConfirmationDialog("Clear All Services",
                "Are you sure you want to clear all registered services?\nThis could break functionality in your application.",
                "Clear", "Cancel"))
            {
                ClearAllServices();
            }
        }

        private void DisplayServices()
        {
            Dictionary<Type, object> services = GetRegisteredServices();
            if (services == null || services.Count == 0)
            {
                EditorGUILayout.HelpBox("No services are currently registered.", MessageType.Info);
                return;
            }
            EditorGUILayout.LabelField($"Registered Services: {services.Count}", subHeaderStyle);
            EditorGUILayout.Space();
            Dictionary<Type, object> servicesCopy = new(services);
            foreach (var kvp in servicesCopy)
            {
                Type serviceType = kvp.Key;
                object serviceInstance = kvp.Value;
                if (!foldoutStates.ContainsKey(serviceType))
                {
                    foldoutStates[serviceType] = false;
                }
                EditorGUILayout.BeginVertical(boxStyle);
                EditorGUILayout.BeginHorizontal();
                foldoutStates[serviceType] = EditorGUILayout.Foldout(foldoutStates[serviceType], serviceType.Name, true, foldoutStyle);
                bool shouldUnregister = false;
                if (GUILayout.Button("Unregister", GUILayout.Width(80)))
                {
                    if (EditorUtility.DisplayDialog("Unregister Service",
                        $"Are you sure you want to unregister the {serviceType.Name} service?",
                        "Unregister", "Cancel"))
                    {
                        shouldUnregister = true;
                    }
                }
                EditorGUILayout.EndHorizontal();
                if (foldoutStates[serviceType])
                {
                    EditorGUI.indentLevel++;
                    EditorGUILayout.LabelField("Type:", serviceType.FullName);
                    EditorGUILayout.LabelField("Instance Type:", serviceInstance.GetType().FullName);
                    if (showDetails)
                    {
                        DisplayObjectDetails(serviceInstance);
                    }
                    EditorGUI.indentLevel--;
                }
                EditorGUILayout.EndVertical();
                EditorGUILayout.Space(5);
                if (shouldUnregister)
                {
                    UnregisterService(serviceType);
                    continue;
                }
            }
        }

        private void DisplayObjectDetails(object obj)
        {
            if (obj == null) return;
            Type type = obj.GetType();
            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("Properties:", EditorStyles.boldLabel);
            PropertyInfo[] properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            if (properties.Length == 0)
            {
                EditorGUILayout.LabelField("No public properties");
            }
            else
            {
                foreach (PropertyInfo property in properties)
                {
                    try
                    {
                        if (property.CanRead)
                        {
                            object value = property.GetValue(obj);
                            EditorGUILayout.LabelField(property.Name, value != null ? value.ToString() : "null");
                        }
                    }
                    catch (Exception)
                    {
                        EditorGUILayout.LabelField(property.Name, "Error reading property");
                    }
                }
            }
            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("Fields:", EditorStyles.boldLabel);
            FieldInfo[] fields = type.GetFields(BindingFlags.Public | BindingFlags.Instance);
            if (fields.Length == 0)
            {
                EditorGUILayout.LabelField("No public fields");
            }
            else
            {
                foreach (FieldInfo field in fields)
                {
                    try
                    {
                        object value = field.GetValue(obj);
                        EditorGUILayout.LabelField(field.Name, value != null ? value.ToString() : "null");
                    }
                    catch (Exception)
                    {
                        EditorGUILayout.LabelField(field.Name, "Error reading field");
                    }
                }
            }
        }

        private Dictionary<Type, object> GetRegisteredServices()
        {
            Type serviceLocatorType = typeof(ServiceLocator);
            FieldInfo servicesField = serviceLocatorType.GetField("services", BindingFlags.NonPublic | BindingFlags.Static);
            if (servicesField != null)
            {
                return servicesField.GetValue(null) as Dictionary<Type, object>;
            }
            return new Dictionary<Type, object>();
        }

        private void UnregisterService(Type serviceType)
        {
            MethodInfo genericMethod = typeof(ServiceLocator).GetMethod("Unregister");
            MethodInfo typedMethod = genericMethod.MakeGenericMethod(serviceType);
            typedMethod.Invoke(null, null);
            Repaint();
        }

        private void ClearAllServices()
        {
            MethodInfo clearMethod = typeof(ServiceLocator).GetMethod("Clear");
            clearMethod.Invoke(null, null);
            Repaint();
        }
    }
}
