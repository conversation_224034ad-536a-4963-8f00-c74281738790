using System.Collections.Generic;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Interface for handling transform preset collections.
    /// </summary>
    /// <typeparam name="T">Type of transform preset.</typeparam>
    public interface ITransformPresetHandler<T>
    {
        /// <summary>
        /// Gets the list of transform presets.
        /// </summary>
        List<T> Presets { get; }

        /// <summary>
        /// Adds a transform preset to the collection.
        /// </summary>
        /// <param name="preset">The transform preset to add.</param>
        void AddPreset(T preset);

        /// <summary>
        /// Removes a transform preset from the collection.
        /// </summary>
        /// <param name="preset">The transform preset to remove.</param>
        void RemovePreset(T preset);

        /// <summary>
        /// Removes all transform presets from the collection.
        /// </summary>
        void RemoveAllPresets();
    }
}