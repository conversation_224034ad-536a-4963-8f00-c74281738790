using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Provides utility methods for string operations.
    /// </summary>
    public static class StringUtility
    {
        /// <summary>
        /// Calculates the Levenshtein distance between two strings.
        /// </summary>
        /// <param name="s">First string.</param>
        /// <param name="t">Second string.</param>
        /// <returns>The Levenshtein distance.</returns>
        public static int Levenshtein(string s, string t)
        {
            if (string.IsNullOrEmpty(s)) return t?.Length ?? 0;
            if (string.IsNullOrEmpty(t)) return s.Length;
            var d = new int[s.Length + 1, t.Length + 1];
            for (var i = 0; i <= s.Length; i++) d[i, 0] = i;
            for (var j = 0; j <= t.Length; j++) d[0, j] = j;
            for (var i = 1; i <= s.Length; i++)
                for (var j = 1; j <= t.Length; j++)
                {
                    var cost = s[i - 1] == t[j - 1] ? 0 : 1;
                    d[i, j] = Mathf.Min(
                        d[i - 1, j] + 1,
                        d[i, j - 1] + 1,
                        d[i - 1, j - 1] + cost
                    );
                }
            return d[s.Length, t.Length];
        }

        /// <summary>
        /// Reverses the specified string.
        /// </summary>
        /// <param name="input">The string to reverse.</param>
        /// <returns>The reversed string.</returns>
        public static string Reverse(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;
            var chars = input.ToCharArray();
            System.Array.Reverse(chars);
            return new string(chars);
        }

        /// <summary>
        /// Determines whether the specified string is a palindrome.
        /// </summary>
        /// <param name="input">The string to check.</param>
        /// <returns>True if the string is a palindrome; otherwise, false.</returns>
        public static bool IsPalindrome(string input)
        {
            if (string.IsNullOrEmpty(input)) return true;
            int left = 0;
            int right = input.Length - 1;
            while (left < right)
            {
                if (input[left] != input[right])
                    return false;
                left++;
                right--;
            }
            return true;
        }

        /// <summary>
        /// Removes all whitespace characters from the specified string.
        /// </summary>
        /// <param name="input">The string to process.</param>
        /// <returns>The string without whitespace.</returns>
        public static string RemoveWhitespace(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;
            System.Text.StringBuilder sb = new System.Text.StringBuilder(input.Length);
            foreach (char c in input)
            {
                if (!char.IsWhiteSpace(c))
                    sb.Append(c);
            }
            return sb.ToString();
        }

        /// <summary>
        /// Generates a random string of the specified length using the provided character set.
        /// </summary>
        /// <param name="length">The length of the random string.</param>
        /// <param name="characterSet">
        /// The set of characters to use. If null or empty, uses the default set:
        /// "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+[]{}|;:',.<>/?`~"
        /// </param>
        /// <returns>A random string.</returns>
        public static string RandomString(int length, string characterSet = null)
        {
            if (length <= 0) return string.Empty;
            const string defaultSet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+[]{}|;:',.<>/?`~";
            var chars = string.IsNullOrEmpty(characterSet) ? defaultSet : characterSet;
            System.Text.StringBuilder sb = new System.Text.StringBuilder(length);
            var rng = new System.Random();
            for (int i = 0; i < length; i++)
            {
                sb.Append(chars[rng.Next(chars.Length)]);
            }
            return sb.ToString();
        }

        /// <summary>
        /// Repeats the specified string a given number of times.
        /// </summary>
        /// <param name="input">The string to repeat.</param>
        /// <param name="count">The number of times to repeat.</param>
        /// <returns>The repeated string.</returns>
        public static string Repeat(string input, int count)
        {
            if (string.IsNullOrEmpty(input) || count <= 0) return string.Empty;
            System.Text.StringBuilder sb = new System.Text.StringBuilder(input.Length * count);
            for (int i = 0; i < count; i++)
            {
                sb.Append(input);
            }
            return sb.ToString();
        }

        /// <summary>
        /// Returns a new string with the characters of the input string randomly shuffled.
        /// </summary>
        /// <param name="input">The string to shuffle.</param>
        /// <returns>The shuffled string.</returns>
        public static string Shuffle(string input)
        {
            if (string.IsNullOrEmpty(input) || input.Length == 1) return input;
            var chars = input.ToCharArray();
            var rng = new System.Random();
            for (int i = chars.Length - 1; i > 0; i--)
            {
                int j = rng.Next(i + 1);
                var temp = chars[i];
                chars[i] = chars[j];
                chars[j] = temp;
            }
            return new string(chars);
        }
    }
}