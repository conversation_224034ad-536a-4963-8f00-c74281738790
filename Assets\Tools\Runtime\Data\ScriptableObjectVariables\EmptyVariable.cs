using UnityEngine;
using System;

namespace SmartVertex.Tools
{
    /// <summary>
    /// A ScriptableObject variable that holds an Empty struct.
    /// Useful for signaling events without passing data.
    /// </summary>
    [CreateAssetMenu(fileName = "EmptyVariable", menuName = "Scriptable Objects/Variables/Empty Variable")]
    public sealed class EmptyVariable : BaseVariable<Empty>
    {
        /// <summary>
        /// Trigger the variable's OnValueChanged event
        /// </summary>
        public void Trigger()
        {
            // Force the event to trigger by setting to a new instance
            // Use the property to ensure the event is triggered
            Value = new Empty();
        }
    }

    /// <summary>
    /// An empty struct used for signaling without data
    /// </summary>
    [Serializable]
    public struct Empty
    {
        // This struct is intentionally empty
        // It's used for signaling events without passing data
    }
}