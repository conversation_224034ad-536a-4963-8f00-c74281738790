using UnityEngine;
using UnityEngine.Events;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Base class for rewarded advertisement implementations.
    /// Provides common functionality for rewarded ad display and reward handling.
    /// </summary>
    public abstract class BaseRewardedAd : BaseAd
    {
        [SerializeField]
        protected UnityEvent onAdReward = new();

        /// <summary>
        /// Gets the event triggered when the user earns a reward by watching the ad.
        /// </summary>
        public UnityEvent OnAdReward => onAdReward;
    }
}