using System.Collections;
using Game.Managers;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to scale a character or object.
    /// </summary>
    public class ScaleCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the scale command.</summary>
        public ScaleParams Parameters { get; }

        /// <summary>Creates a new ScaleCommand.</summary>
        /// <param name="parameters">Scale parameters.</param>
        public ScaleCommand(ScaleParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            GameObject target = ObjectManager.Instance.FindById(Parameters.targetId);
            if (target == null)
            {
                Debug.LogWarning($"ScaleCommand: Target object with ID '{Parameters.targetId}' not found.");
                yield break;
            }

            target.transform.localScale = Parameters.scale;
            yield break;
        }
    }

    /// <summary>Parameters for Scale command.</summary>
    [System.Serializable]
    public struct ScaleParams
    {
        public string targetId;
        public Vector3 scale;
    }
}
