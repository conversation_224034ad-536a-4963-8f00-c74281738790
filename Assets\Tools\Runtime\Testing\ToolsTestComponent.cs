using System;
using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Test component for demonstrating and testing the ServiceLocator and EventBus tools.
    /// Attach this to a GameObject in your scene to test the editor tools.
    /// </summary>
    public class ToolsTestComponent : MonoBehaviour
    {
        [SerializeField] private bool registerServicesOnStart = true;
        [SerializeField] private bool subscribeToEventsOnStart = true;

        // Sample service interfaces
        public interface ILogService : IService
        {
            void Log(string message);
        }

        public interface IDataService : IService
        {
            string GetData();
            void SetData(string data);
        }

        // Sample event classes
        [Serializable]
        public class PlayerEvent
        {
            [SerializeField]
            private string playerName;

            [SerializeField]
            private int score;

            public string PlayerName
            {
                get => playerName;
                set => playerName = value;
            }

            public int Score
            {
                get => score;
                set => score = value;
            }

            public PlayerEvent(string playerName, int score)
            {
                this.playerName = playerName;
                this.score = score;
            }

            public override string ToString() => $"Player: {playerName}, Score: {score}";
        }

        [Serializable]
        public class GameEvent
        {
            [SerializeField]
            private string eventName;

            [SerializeField]
            private float timeStamp;

            public string EventName
            {
                get => eventName;
                set => eventName = value;
            }

            public float TimeStamp
            {
                get => timeStamp;
                set => timeStamp = value;
            }

            public GameEvent(string eventName)
            {
                this.eventName = eventName;
                this.timeStamp = Time.time;
            }

            public override string ToString() => $"Event: {eventName}, Time: {timeStamp:F2}";
        }

        // Sample service implementations
        private class LogService : ILogService
        {
            private string lastMessage;

            public string LastMessage
            {
                get => lastMessage;
                private set => lastMessage = value;
            }

            public void Log(string message)
            {
                lastMessage = message;
                Debug.Log($"[LogService] {message}");
            }

            public void Register()
            {
                ServiceLocator.Register<ILogService>(this);
            }

            public void Unregister()
            {
                ServiceLocator.Unregister<ILogService>();
            }
        }

        private class DataService : IDataService
        {
            private string data = "Default Data";

            public string GetData() => data;

            public void SetData(string newData)
            {
                data = newData;
            }

            public void Register()
            {
                ServiceLocator.Register<IDataService>(this);
            }

            public void Unregister()
            {
                ServiceLocator.Unregister<IDataService>();
            }
        }

        // Keep references to services
        private LogService logService;
        private DataService dataService;

        // Event handlers
        private void OnPlayerEvent(PlayerEvent evt)
        {
            Debug.Log($"Received PlayerEvent: {evt}");

            if (ServiceLocator.TryGet<ILogService>(out var logService))
            {
                logService.Log($"Player {evt.PlayerName} scored {evt.Score} points");
            }
        }

        private void OnGameEvent(GameEvent evt)
        {
            Debug.Log($"Received GameEvent: {evt}");

            if (ServiceLocator.TryGet<ILogService>(out var logService))
            {
                logService.Log($"Game event: {evt.EventName} at {evt.TimeStamp:F2}s");
            }
        }

        private void Start()
        {
            if (registerServicesOnStart)
            {
                RegisterServices();
            }

            if (subscribeToEventsOnStart)
            {
                SubscribeToEvents();
            }
        }

        private void OnDestroy()
        {
            UnregisterServices();
            UnsubscribeFromEvents();
        }

        public void RegisterServices()
        {
            logService = new LogService();
            dataService = new DataService();

            logService.Register();
            dataService.Register();

            Debug.Log("Services registered with ServiceLocator");
        }

        public void UnregisterServices()
        {
            if (logService != null)
            {
                logService.Unregister();
                logService = null;
            }

            if (dataService != null)
            {
                dataService.Unregister();
                dataService = null;
            }

            Debug.Log("Services unregistered from ServiceLocator");
        }

        public void SubscribeToEvents()
        {
            EventBus.Subscribe<PlayerEvent>(OnPlayerEvent);
            EventBus.Subscribe<GameEvent>(OnGameEvent);

            Debug.Log("Subscribed to events in EventBus");
        }

        public void UnsubscribeFromEvents()
        {
            EventBus.Unsubscribe<PlayerEvent>(OnPlayerEvent);
            EventBus.Unsubscribe<GameEvent>(OnGameEvent);

            Debug.Log("Unsubscribed from events in EventBus");
        }

        public void PublishPlayerEvent()
        {
            var playerEvent = new PlayerEvent("Player1", UnityEngine.Random.Range(0, 100));
            EventBus.Publish(playerEvent);
        }

        public void PublishGameEvent()
        {
            string[] eventNames = { "GameStart", "LevelComplete", "GameOver", "Achievement", "Checkpoint" };
            int index = UnityEngine.Random.Range(0, eventNames.Length);
            var gameEvent = new GameEvent(eventNames[index]);
            EventBus.Publish(gameEvent);
        }

        public void UpdateDataService()
        {
            if (ServiceLocator.TryGet<IDataService>(out var dataService))
            {
                string newData = $"Updated data at {DateTime.Now.ToShortTimeString()}";
                dataService.SetData(newData);
                Debug.Log($"Updated data service: {newData}");
            }
            else
            {
                Debug.LogWarning("DataService not found in ServiceLocator");
            }
        }

        // OnGUI for testing in play mode
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 400));
            GUILayout.Label("ServiceLocator & EventBus Test", GUI.skin.box);
            GUILayout.Space(10);

            // Service management
            GUILayout.Label("Service Management:", GUI.skin.box);
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Register Services"))
            {
                RegisterServices();
            }
            if (GUILayout.Button("Unregister Services"))
            {
                UnregisterServices();
            }
            GUILayout.EndHorizontal();

            if (GUILayout.Button("Update Data Service"))
            {
                UpdateDataService();
            }

            GUILayout.Space(10);

            // Event management
            GUILayout.Label("Event Management:", GUI.skin.box);
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Subscribe to Events"))
            {
                SubscribeToEvents();
            }
            if (GUILayout.Button("Unsubscribe from Events"))
            {
                UnsubscribeFromEvents();
            }
            GUILayout.EndHorizontal();

            GUILayout.Space(10);

            // Event publishing
            GUILayout.Label("Publish Events:", GUI.skin.box);
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Publish Player Event"))
            {
                PublishPlayerEvent();
            }
            if (GUILayout.Button("Publish Game Event"))
            {
                PublishGameEvent();
            }
            GUILayout.EndHorizontal();

            GUILayout.Space(10);

            // Open debug windows
            GUILayout.Label("Debug Windows:", GUI.skin.box);
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Open Service Locator Window"))
            {
#if UNITY_EDITOR
                UnityEditor.EditorApplication.ExecuteMenuItem("Tools/Service Locator Debugger");
#endif
            }
            if (GUILayout.Button("Open Event Bus Window"))
            {
#if UNITY_EDITOR
                UnityEditor.EditorApplication.ExecuteMenuItem("Tools/Event Bus Debugger");
#endif
            }
            GUILayout.EndHorizontal();

            GUILayout.EndArea();
        }
    }
}
