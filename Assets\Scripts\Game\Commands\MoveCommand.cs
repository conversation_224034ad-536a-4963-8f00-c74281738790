using System;
using System.Collections;
using Game.Managers;
using SmartVertex.Tools;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to move an object to a target position over a duration using damping.
    /// </summary>
    public class MoveCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the move command.</summary>
        public MoveParams Parameters { get; }

        /// <summary>Creates a new MoveCommand.</summary>
        /// <param name="parameters">Move parameters.</param>
        public MoveCommand(MoveParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            string sceneId = ObjectManager.Instance.GetSceneIdForEntity(Parameters.objectId);
            GameObject sceneObject = ObjectManager.Instance.FindScene(sceneId);
            GameObject selectedObject = ObjectManager.Instance.FindById(Parameters.objectId);
            Vector2Int gridSize = ObjectManager.Instance.SceneGridSize;

            Vector3 newPosition = GridUtility.GetWorldPosition
            (sceneObject, gridSize, Parameters.targetPosition, GridUtility.GridPlane.XY);

            if (selectedObject == null)
            {
                Debug.LogWarning($"Object with ID '{Parameters.objectId}' not found for move command.");
                yield break;
            }

            // Use damping-based movement (damping = time to reach target)
            while (Vector3.Distance(selectedObject.transform.position, newPosition) > 0.01f)
            {
                // Calculate lerp factor based on damping time
                float lerpFactor = Parameters.damping > 0 ? Time.deltaTime / Parameters.damping : 1f;

                selectedObject.transform.position = Vector3.Lerp(
                    selectedObject.transform.position,
                    newPosition,
                    lerpFactor
                );
                yield return null;
            }

            // Ensure final position is exact
            selectedObject.transform.position = newPosition;
        }
    }

    /// <summary>Parameters for Move command.</summary>
    [Serializable]
    public struct MoveParams
    {
        public string objectId;
        public Vector2Int targetPosition;
        public float damping;
    }
}
