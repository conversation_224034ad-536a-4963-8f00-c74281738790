using System;
using System.Collections;
using Game.Managers;

namespace Game.CommandSystem
{
    /// <summary>
    /// Defines different speeds for camera focus transitions.
    /// </summary>
    public enum FocusSpeed
    {
        Instant = 0,
        Fast = 1,
        Slow = 2
    }

    /// <summary>
    /// Defines different types of camera shake intensities.
    /// </summary>
    public enum ShakeType
    {
        Stable = 0,
        Light = 1,
        Medium = 2,
        Heavy = 3
    }

    /// <summary>
    /// Defines different camera distance presets from the target.
    /// </summary>
    public enum FocusDistance
    {
        Close = 0,
        Medium = 1,
        Far = 2
    }

    /// <summary>
    /// Parameters container for the Focus command configuration.
    /// </summary>
    [Serializable]
    public class FocusParams
    {
        /// <summary>
        /// The unique identifier of the target to focus on.
        /// </summary>
        public string targetId;

        /// <summary>
        /// The speed at which the camera transitions to the target.
        /// </summary>
        public FocusSpeed focusSpeed = FocusSpeed.Instant;

        /// <summary>
        /// The type of camera shake to apply during or after focus.
        /// </summary>
        public ShakeType shakeType = ShakeType.Light;

        /// <summary>
        /// The distance preset to maintain from the target.
        /// </summary>
        public FocusDistance focusDistance = FocusDistance.Medium;
    }

    /// <summary>
    /// Command to focus the camera on a specified target with configurable parameters.
    /// </summary>
    public class FocusCommand : ICoroutineCommand
    {
        /// <summary>
        /// Gets the parameters for this focus command.
        /// </summary>
        public FocusParams Parameters { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="FocusCommand"/> class.
        /// </summary>
        /// <param name="parameters">The focus operation parameters.</param>
        public FocusCommand(FocusParams parameters)
        {
            Parameters = parameters ?? throw new ArgumentNullException(nameof(parameters));
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            yield return CameraManager.Instance.FocusOnTarget(
                Parameters.targetId,
                Parameters.focusSpeed,
                Parameters.shakeType,
                Parameters.focusDistance
            );
        }
    }
}
