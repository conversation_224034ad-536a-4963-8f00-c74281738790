using System.Collections;
using Game.Managers;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to destroy an object or character.
    /// </summary>
    public class DestroyCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the destroy command.</summary>
        public DestroyParams Parameters { get; }

        /// <summary>Creates a new DestroyCommand.</summary>
        /// <param name="parameters">Destroy parameters.</param>
        public DestroyCommand(DestroyParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            ObjectManager.Instance.DestroyObject(Parameters.objectId);
            yield break;
        }
    }

    /// <summary>Parameters for Destroy command.</summary>
    [System.Serializable]
    public struct DestroyParams
    {
        public string objectId;
    }
}
