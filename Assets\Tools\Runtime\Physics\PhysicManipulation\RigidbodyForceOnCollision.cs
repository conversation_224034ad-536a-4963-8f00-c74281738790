using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Applies a configurable force to another Rigidbody upon collision, using the contact normal and an optional offset.
    /// Attach this script to any GameObject with a Rigidbody.
    /// </summary>
    [RequireComponent(typeof(Rigidbody))]
    public class RigidbodyForceOnCollision : MonoBehaviour
    {
        [Tooltip("The magnitude of the force to apply.")]
        [SerializeField]
        private float forceMagnitude = 10f;

        [<PERSON>lt<PERSON>("The type of force to apply.")]
        [SerializeField]
        private ForceMode forceMode = ForceMode.Impulse;

        [Tooltip("If true, uses the last contact's normal instead of the first contact's normal.")]
        [SerializeField]
        private bool useLastContactNormal = false;

        [Tooltip("Offset to apply to the collision's contact normal.")]
        [SerializeField]
        private Vector3 forceDirectionOffset = Vector3.zero;

        /// <summary>
        /// The magnitude of the force to apply.
        /// </summary>
        public float ForceMagnitude
        {
            get => forceMagnitude;
            set => forceMagnitude = value;
        }

        /// <summary>
        /// The type of force to apply.
        /// </summary>
        public ForceMode ForceMode
        {
            get => forceMode;
            set => forceMode = value;
        }

        /// <summary>
        /// If true, uses the last contact's normal instead of the first contact's normal.
        /// </summary>
        public bool UseLastContactNormal
        {
            get => useLastContactNormal;
            set => useLastContactNormal = value;
        }

        /// <summary>
        /// Offset to apply to the collision's contact normal.
        /// </summary>
        public Vector3 ForceDirectionOffset
        {
            get => forceDirectionOffset;
            set => forceDirectionOffset = value;
        }

        /// <summary>
        /// Called when this collider/rigidbody has begun touching another rigidbody/collider.
        /// </summary>
        /// <param name="collision">Collision data associated with the collision event.</param>
        private void OnCollisionEnter(Collision collision)
        {
            if (!isActiveAndEnabled)
                return;

            Rigidbody otherRigidbody = collision.rigidbody;
            if (otherRigidbody == null)
                return;

            int contactIndex = useLastContactNormal ? collision.contactCount - 1 : 0;
            Vector3 forceDirection = (collision.contacts[contactIndex].normal + forceDirectionOffset).normalized;
            otherRigidbody.AddForce(forceDirection * forceMagnitude, forceMode);
        }
    }
}