using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using Game.Managers;
using SmartVertex.Tools;
using UnityEngine;
using UnityEngine.Events;

namespace Game.CommandSystem
{
    /// <summary>
    /// Manages the execution of command sequences.
    /// </summary>
    public class CommandManager : Singleton<CommandManager>
    {
        /// <summary>
        /// Gets or sets the CommandsJson string. Setting this will re-parse the commands.
        /// </summary>
        public string CommandsJson
        {
            get => commandsJson;
            set
            {
                commandsJson = value;
                ParseCommands();
            }
        }

        /// <summary>
        /// Event triggered when the command cache is generated.
        /// </summary>
        public UnityEvent OnCacheGenerated { get; set; } = new();

        /// <summary>
        /// Gets the command interpreter instance.
        /// </summary>
        public CommandInterpreter CommandInterpreter => commandInterpreter;

        /// <summary>
        /// Gets the current command sequence.
        /// </summary>
        public List<ICoroutineCommand> CommandSequence => commandSequence;

        private CommandInterpreter commandInterpreter = new();
        private List<ICoroutineCommand> commandSequence = new();
        private string commandsJson;
        private Coroutine currentSequenceCoroutine;

        /// <summary>
        /// Executes the current command sequence as a coroutine.
        /// </summary>
        public void ExecuteSequence()
        {
            if (commandSequence.Count == 0)
            {
                Debug.LogWarning("Command sequence is empty. Cannot execute.");
                return;
            }

            if (currentSequenceCoroutine != null)
            {
                StopCoroutine(currentSequenceCoroutine);
            }

            currentSequenceCoroutine = StartCoroutine(RunSequence(commandSequence));
        }

        private async void ParseCommands()
        {
            commandSequence = commandInterpreter.Parse(commandsJson);
            if (commandSequence.Count == 0)
            {
                Debug.LogWarning("No commands parsed from CLI JSON.");
            }
            else
            {
                Debug.Log($"Parsed {commandSequence.Count} commands from CLI JSON.");

                await AudioManager.Instance.GenerateCache(commandSequence);
                await ObjectManager.Instance.GenerateCache(commandSequence);
                OnCacheGenerated.Invoke();
            }
        }

        private IEnumerator RunSequence(List<ICoroutineCommand> commands)
        {
            Debug.Log("--- Starting Command Sequence ---");

            foreach (var command in commands)
            {
                yield return StartCoroutine(command.Execute());
            }

            currentSequenceCoroutine = null;
            Debug.Log("--- Command Sequence Finished ---");
        }
    }
}