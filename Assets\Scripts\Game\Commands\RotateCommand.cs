using System.Collections;
using Game.Managers;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to rotate a character or object.
    /// </summary>
    public class RotateCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the rotate command.</summary>
        public RotateParams Parameters { get; }

        /// <summary>Creates a new RotateCommand.</summary>
        /// <param name="parameters">Rotate parameters.</param>
        public RotateCommand(RotateParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            GameObject targetObject = ObjectManager.Instance.FindById(Parameters.targetId);
            if (targetObject == null)
            {
                Debug.LogWarning($"RotateCommand: Target object with ID '{Parameters.targetId}' not found.");
                yield break;
            }

            float rotationZ = 0f;
            Vector3 currentScale = targetObject.transform.localScale;

            switch (Parameters.rotation)
            {
                case Rotation.Left:
                    if (!Parameters.isCharacter)
                        rotationZ = 180f;
                    else
                        currentScale = new Vector3(Mathf.Abs(currentScale.x) * -1, currentScale.y, currentScale.z);
                    break;
                case Rotation.Right:
                    if (!Parameters.isCharacter)
                        rotationZ = 0f;
                    else
                        currentScale = new Vector3(Mathf.Abs(currentScale.x), currentScale.y, currentScale.z);
                    break;
                case Rotation.Up:
                    rotationZ = 90f;
                    break;
                case Rotation.Down:
                    rotationZ = 270f;
                    break;
            }

            targetObject.transform.rotation = Quaternion.Euler(0f, 0f, rotationZ);
            targetObject.transform.localScale = currentScale;
            yield break;
        }
    }

    /// <summary>
    /// Parameters for Rotate command.
    /// </summary>
    [System.Serializable]
    public struct RotateParams
    {
        public string targetId;
        public Rotation rotation;
        public bool isCharacter;
    }

    public enum Rotation
    {
        Left,
        Right,
        Up,
        Down
    }
}
