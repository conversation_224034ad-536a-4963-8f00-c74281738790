namespace SmartVertex.Tools
{
    /// <summary>
    /// Interface for services that can register and unregister themselves with the ServiceLocator.
    ///
    /// <para>
    /// Implementing this interface allows services to manage their own registration lifecycle
    /// with the ServiceLocator, making it easier to handle initialization and cleanup.
    /// </para>
    /// </summary>
    public interface IService
    {
        /// <summary>
        /// Registers this service with the ServiceLocator.
        /// </summary>
        /// <remarks>
        /// Implementations should typically call ServiceLocator.Register with the appropriate interface type.
        /// </remarks>
        void Register();

        /// <summary>
        /// Unregisters this service from the ServiceLocator.
        /// </summary>
        /// <remarks>
        /// Implementations should typically call ServiceLocator.Unregister with the appropriate interface type.
        /// </remarks>
        void Unregister();
    }
}
