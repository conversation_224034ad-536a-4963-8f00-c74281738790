using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// MonoBehaviour for testing OpenAITextToSpeech functionality in the Unity Editor.
    /// </summary>
    public class OpenAITextToSpeechTester : MonoBehaviour
    {
        [Header("OpenAI TTS Settings")]
        [SerializeField] private string apiKey;
        [SerializeField] private string apiUrl = "https://api.openai.com/v1/audio/speech";
        [SerializeField] private string model = "tts-1";
        [SerializeField] private string inputText = "Hello, this is a test.";
        [SerializeField] private string voice = "alloy";
        [SerializeField] private string instructions = "";
        [SerializeField] private float speed = 1.0f;
        [SerializeField] private bool speakOnEnable = true;

        private AudioSource audioSource;

        private void OnEnable()
        {
            if (speakOnEnable)
                Speak();
        }

        /// <summary>
        /// Triggers TTS generation and plays the result. Can be called from the Unity Editor context menu.
        /// </summary>
        [ContextMenu("Speak")]
        public async void Speak()
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }

            var ttsRequest = new OpenAITextToSpeech.TTSRequest
            {
                model = model,
                input = inputText,
                voice = voice,
                instructions = instructions,
                response_format = "mp3",
                speed = speed
            };

            var ttsService = new OpenAITextToSpeech(ttsRequest, apiKey, apiUrl);
            AudioClip clip = await ttsService.Generate();
            if (clip != null)
            {
                audioSource.clip = clip;
                audioSource.Play();
                Debug.Log("TTS audio played successfully.");
            }
            else
            {
                Debug.LogError("Failed to generate TTS audio.");
            }
        }
    }
}
