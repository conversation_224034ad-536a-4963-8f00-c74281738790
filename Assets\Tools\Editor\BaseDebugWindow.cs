using UnityEditor;
using UnityEngine;

namespace SmartVertex.EditorTools
{
    /// <summary>
    /// Base class for debug windows that provides common functionality for UI styling and layout.
    /// </summary>
    public abstract class BaseDebugWindow : EditorWindow
    {
        /// <summary>Scroll position for scrollable areas.</summary>
        protected Vector2 scrollPosition;
        /// <summary>Header style for window titles.</summary>
        protected GUIStyle headerStyle;
        /// <summary>Sub-header style for section titles.</summary>
        protected GUIStyle subHeaderStyle;
        /// <summary>Button style for custom buttons.</summary>
        protected GUIStyle buttonStyle;
        /// <summary>Box style for grouping UI elements.</summary>
        protected GUIStyle boxStyle;
        /// <summary>Foldout style for collapsible sections.</summary>
        protected GUIStyle foldoutStyle;
        /// <summary>Refresh icon content.</summary>
        protected GUIContent refreshIcon;
        /// <summary>Clear icon content.</summary>
        protected GUIContent clearIcon;

        /// <summary>
        /// Called when the window is enabled. Loads icons and sets tooltips.
        /// </summary>
        protected virtual void OnEnable()
        {
            refreshIcon = EditorGUIUtility.IconContent("Refresh");
            refreshIcon.tooltip = "Refresh";
            clearIcon = EditorGUIUtility.IconContent("d_TreeEditor.Trash");
            clearIcon.tooltip = "Clear";
        }

        /// <summary>
        /// Initializes GUI styles if they are not already set.
        /// </summary>
        protected void InitializeStyles()
        {
            if (headerStyle == null)
            {
                headerStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 14,
                    margin = new RectOffset(5, 5, 5, 5)
                };
            }
            if (subHeaderStyle == null)
            {
                subHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 12,
                    margin = new RectOffset(5, 5, 2, 2)
                };
            }
            if (buttonStyle == null)
            {
                buttonStyle = new GUIStyle(GUI.skin.button)
                {
                    padding = new RectOffset(8, 8, 4, 4)
                };
            }
            if (boxStyle == null)
            {
                boxStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    padding = new RectOffset(10, 10, 10, 10),
                    margin = new RectOffset(5, 5, 5, 5)
                };
            }
            if (foldoutStyle == null)
            {
                foldoutStyle = new GUIStyle(EditorStyles.foldout)
                {
                    fontStyle = FontStyle.Bold
                };
            }
        }

        /// <summary>
        /// Draws a header with refresh and clear buttons.
        /// </summary>
        /// <param name="title">The title to display.</param>
        /// <param name="refreshTooltip">Tooltip for the refresh button.</param>
        /// <param name="clearTooltip">Tooltip for the clear button.</param>
        protected void DrawHeader(string title, string refreshTooltip, string clearTooltip)
        {
            EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
            GUILayout.Label(title, headerStyle);
            GUILayout.FlexibleSpace();
            if (GUILayout.Button(refreshIcon, EditorStyles.toolbarButton))
            {
                OnRefreshClicked();
            }
            if (GUILayout.Button(clearIcon, EditorStyles.toolbarButton))
            {
                OnClearClicked();
            }
            EditorGUILayout.EndHorizontal();
        }

        /// <summary>
        /// Called when the refresh button is clicked. Override to implement custom refresh logic.
        /// </summary>
        protected virtual void OnRefreshClicked()
        {
            Repaint();
        }

        /// <summary>
        /// Called when the clear button is clicked. Override to implement custom clear logic.
        /// </summary>
        protected virtual void OnClearClicked()
        {
            // Override in derived classes
        }

        /// <summary>
        /// Displays a confirmation dialog.
        /// </summary>
        /// <param name="title">Dialog title.</param>
        /// <param name="message">Dialog message.</param>
        /// <param name="okButton">OK button text.</param>
        /// <param name="cancelButton">Cancel button text.</param>
        /// <returns>True if OK was pressed, false otherwise.</returns>
        protected bool DisplayConfirmationDialog(string title, string message, string okButton, string cancelButton)
        {
            return EditorUtility.DisplayDialog(title, message, okButton, cancelButton);
        }
    }
}
