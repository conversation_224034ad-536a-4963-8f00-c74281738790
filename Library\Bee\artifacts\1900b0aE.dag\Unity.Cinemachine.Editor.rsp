-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.ref.dll"
-define:UNITY_6000_1_0
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:CINEMACHINE_TIMELINE
-define:CINEMACHINE_PHYSICS_2D
-define:CINEMACHINE_PHYSICS
-define:CINEMACHINE_UGUI
-define:CINEMACHINE_URP
-define:CINEMACHINE_TIMELINE_1_8_2
-define:CINEMACHINE_UNITY_ANIMATION
-define:CINEMACHINE_UNITY_INPUTSYSTEM
-define:CINEMACHINE_3_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-define:UNITY_EDITOR_ONLY_COMPILATION
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AMDModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.Apple.Extensions.Common.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"D:/Unity/Editors/6000.1.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.visualscripting@7dcdc439b230/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@7dcdc439b230/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@7dcdc439b230/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@7dcdc439b230/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"D:/Unity/Editors/6000.1.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/AssemblyInfo.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineBasicMultiChannelPerlinEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineBlenderSettingsEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineBrainEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineBrainEventsEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineCameraEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineCameraManagerEventsEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineClearShotEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineComponentBaseEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineConfiner2DEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineConfiner3DEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineDeoccluderEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineExtensionEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineFollowEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineFreeLookModifierEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineGroupFramingEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineHardLookAtEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineMixingCameraEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineNoiseSettingsEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineOrbitalFollowEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachinePanTiltEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachinePixelPerfectEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachinePositionComposerEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineRotationComposerEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineSequencerCameraEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineSplineCartEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineSplineDollyEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineSplineDollyLookAtTargetsEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineSplineRollEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineSplineSmootherEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineStateDrivenCameraEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineStoryboardEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineTargetGroupEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineThirdPersonFollowEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineTriggerActionEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Editors/InputAxisControllerEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Impulse/CinemachineCollisionImpulseSourceEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Impulse/CinemachineImpulseChannelPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Impulse/CinemachineImpulseChannels.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Impulse/CinemachineImpulseDefinitionPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Impulse/CinemachineImpulseEnvelopePropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Impulse/CinemachineImpulseListenerEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Impulse/CinemachineImpulseSourceEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Menus/CinemachineMenu.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/AxisStatePropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/BaseEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/Cinemachine3rdPersonFollowEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachineComposerEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachineConfinerEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachineDollyCartEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachineFramingTransposerEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachineFreeLookEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachineGroupComposerEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachineOrbitalTransposerEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachinePathEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachinePOVEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachineSmoothPathEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachineTool.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachineTrackedDollyEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachineTransposerEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/CinemachineVirtualCameraEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/ObsoleteInspectorUtility.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/ObsoletePropertyDrawers.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/OrbitalTransposerHeadingPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/VcamStageEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Obsolete/VcamTargetPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PostProcessing/CinemachineAutoFocusEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PostProcessing/CinemachinePostProcessingEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PostProcessing/CinemachineVolumeSettingsEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/BlendDefinitionPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/CameraTargetPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/ChildCameraPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/DelayedVectorPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/EmbeddedAssetPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/EmbeddedBlenderSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/FoldoutWithEnabledButtonPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/GroupTargetPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/HideFoldoutPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/HideIfNoComponentPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/InputAxisNamePropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/InputAxisPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/LensSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/MinMaxRangeSliderPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/NoiseSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/OutputChannelsPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/SensorSizePropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/SplineAutoDollyPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/SplineSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/TagFieldPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/ThreeOrbitRigPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/TrackerSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/PropertyDrawers/Vector2AsRangePropertyDrawer.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Samples/ExposeHDRPInternals/MaterialUpgradeHelper.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Samples/SampleDependencyImporter.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/SaveDuringPlay/SaveDuringPlay.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/SceneView/CinemachineCameraViewpoint.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Timeline/CinemachineShotClipEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Timeline/CinemachineShotEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Timeline/CinemachineTrackEditor.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Upgrader/CinemachineUpgradeManager.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Upgrader/UpgradeManagerInspectorHelpers.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Upgrader/UpgradeObjectToCm3.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Upgrader/UpgradeObjectToCm3.Data.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/CinemachineChannelNames.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/CinemachineEditorAnalytics.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/CinemachineLensPalette.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/CinemachinePhysicalLensPalette.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/CinemachineSceneToolHelpers.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/CmCameraInspectorUtility.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/CmPipelineComponentInspectorUtility.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/DelayedFriendlyFieldDragger.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/EmbeddedAssetEditorUtility.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/GameViewComposerGuides.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/IconUtility.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/InspectorUtility.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/ReflectionHelpers.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/ScriptableObjectUtility.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/SerializedPropertyHelper.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Utility/SplineDataInspectorUtility.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Windows/CinemachineComposerPrefs.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Windows/CinemachineCorePrefs.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Windows/CinemachineDeoccluderPrefs.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Windows/CinemachineSettings.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Windows/CinemachineSplineDollyPrefs.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Windows/CinemachineTimelinePrefs.cs"
"Library/PackageCache/com.unity.cinemachine@b66fdb7cd1f2/Editor/Windows/WaveformWindow.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.UnityAdditionalFile.txt"