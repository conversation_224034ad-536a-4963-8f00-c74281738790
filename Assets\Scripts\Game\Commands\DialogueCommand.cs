using System;
using System.Collections;
using Game.Managers;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to display dialogue for a character.
    /// </summary>
    public class DialogueCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the dialogue command.</summary>
        public DialogueParams Parameters { get; }

        /// <summary>Creates a new DialogueCommand.</summary>
        /// <param name="parameters">Dialogue parameters.</param>
        public DialogueCommand(DialogueParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            float duration = 0f;

            AudioManager.Instance.PlayDialogue(Parameters.text, Parameters.characterId, d => { duration = d; });

            if (Parameters.showSubtitle)
            {
                UIManager.Instance.ShowSubtitle(Parameters.text);
            }

            yield return new WaitForSeconds(duration);
        }
    }

    /// <summary>Parameters for Dialogue command.</summary>
    [Serializable]
    public struct DialogueParams
    {
        public string characterId;
        public string text;
        public bool showSubtitle;
    }
}
