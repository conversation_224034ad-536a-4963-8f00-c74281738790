using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace SmartVertex.Tools
{
    /// <summary>
    /// A mapping between a void event channel and a Unity event.
    /// </summary>
    [Serializable]
    public class VoidEventChannelMapping
    {
        /// <summary>
        /// The event channel to listen to.
        /// </summary>
        [SerializeField]
        private VoidEventChannelBase eventChannel;

        /// <summary>
        /// Gets or sets the event channel to listen to.
        /// </summary>
        public VoidEventChannelBase EventChannel
        {
            get => eventChannel;
            set => eventChannel = value;
        }

        /// <summary>
        /// The Unity event to invoke when the event channel raises an event.
        /// </summary>
        [SerializeField]
        private UnityEvent onEventRaised;

        /// <summary>
        /// Gets the Unity event to invoke when the event channel raises an event.
        /// </summary>
        public UnityEvent OnEventRaised => onEventRaised;
    }

    /// <summary>
    /// Event listener that can listen to multiple void event channels.
    /// </summary>
    public sealed class VoidEventListener : MonoBehaviour
    {
        /// <summary>
        /// The list of event channel mappings to listen to.
        /// </summary>
        [SerializeField]
        private List<VoidEventChannelMapping> eventChannelMappings = new();

        /// <summary>
        /// Gets the list of event channel mappings.
        /// </summary>
        public List<VoidEventChannelMapping> EventChannelMappings => eventChannelMappings;

        private void OnEnable()
        {
            SubscribeToEvents();
        }

        private void OnDisable()
        {
            UnsubscribeFromEvents();
        }

        /// <summary>
        /// Subscribes to all event channels in the mappings list.
        /// </summary>
        private void SubscribeToEvents()
        {
            foreach (var mapping in eventChannelMappings)
            {
                if (mapping.EventChannel != null)
                {
                    mapping.EventChannel.OnEventRaised += mapping.OnEventRaised.Invoke;
                }
            }
        }

        /// <summary>
        /// Unsubscribes from all event channels in the mappings list.
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            foreach (var mapping in eventChannelMappings)
            {
                if (mapping.EventChannel != null)
                {
                    mapping.EventChannel.OnEventRaised -= mapping.OnEventRaised.Invoke;
                }
            }
        }
    }
}