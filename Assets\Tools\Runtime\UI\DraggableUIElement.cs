using UnityEngine;
using UnityEngine.EventSystems;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Allows a UI element to be dragged within its parent canvas.
    /// </summary>
    public class DraggableUIElement : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IBeginDrag<PERSON><PERSON><PERSON>, IDragHandler
    {
        /// <summary>
        /// If true, the UI element will be set as the last sibling when dragged, so it will be rendered on top of other UI elements.
        /// </summary>
        public bool SetAsLastSibling
        {
            get => setAsLastSibling;
            set => setAsLastSibling = value;
        }

        /// <summary>
        /// If assigned, movements will be constrained to this RectTransform, but input events will still be received by the original RectTransform.
        /// </summary>
        public RectTransform OverrideRectTransform
        {
            get => overrideRectTransform;
            set => overrideRectTransform = value;
        }

        [Tooltip("If true, the UI element will be set as the last sibling when dragged, so it will be rendered on top of other UI elements.")]
        [SerializeField] private bool setAsLastSibling = false;

        [Tooltip("If assigned, movements will be constrained to this RectTransform, but input events will still be received by the original RectTransform which this script is attached to.")]
        [SerializeField] private RectTransform overrideRectTransform;

        private RectTransform rectTransform;
        private Canvas parentCanvas;
        private Vector2 pointerOffset;

        private void Awake()
        {
            rectTransform = GetComponent<RectTransform>();
            parentCanvas = GetComponentInParent<Canvas>();
            if (parentCanvas == null)
            {
                Debug.LogError("DraggableUIElement: No Canvas found in parent.");
            }
        }

        /// <summary>
        /// Called when the drag operation begins.
        /// </summary>
        /// <param name="eventData">Pointer event data.</param>
        public void OnBeginDrag(PointerEventData eventData)
        {
            var targetRect = GetTargetRectTransform();
            if (targetRect == null)
                return;

            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
                parentCanvas.transform as RectTransform,
                eventData.position,
                parentCanvas.worldCamera,
                out var localPointerPos))
            {
                pointerOffset = targetRect.localPosition - (Vector3)localPointerPos;
            }
        }

        /// <summary>
        /// Called every time the pointer moves during dragging.
        /// </summary>
        /// <param name="eventData">Pointer event data.</param>
        public void OnDrag(PointerEventData eventData)
        {
            var targetRect = GetTargetRectTransform();
            if (targetRect == null)
                return;

            if (setAsLastSibling)
                transform.SetAsLastSibling();

            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
                parentCanvas.transform as RectTransform,
                eventData.position,
                parentCanvas.worldCamera,
                out var localPointerPos))
            {
                targetRect.localPosition = localPointerPos + pointerOffset;
            }
        }

        private RectTransform GetTargetRectTransform()
        {
            var targetRect = overrideRectTransform == null ? rectTransform : overrideRectTransform;
            if (targetRect == null)
            {
                Debug.LogError("DraggableUIElement: Target RectTransform is null. Please attach this script to a UI element or assign an override RectTransform.");
                return null;
            }
            return targetRect;
        }
    }
}