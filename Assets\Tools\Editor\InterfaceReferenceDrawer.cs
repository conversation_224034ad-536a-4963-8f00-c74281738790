using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using SmartVertex.Tools;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace SmartVertex.EditorTools
{
    /// <summary>
    /// Custom property drawer for InterfaceReference types.
    /// <PERSON>les drawing the interface reference field in the inspector.
    /// </summary>
    [CustomPropertyDrawer(typeof(InterfaceReference<>))]
    [CustomPropertyDrawer(typeof(InterfaceReference<,>))]
    public sealed class InterfaceReferenceDrawer : PropertyDrawer
    {
        private const string UnderlyingValueField = "underlyingValue";
        private static readonly Dictionary<Type, InterfaceArgs> typeCache = new();

        /// <summary>
        /// Draws the property in the inspector.
        /// </summary>
        /// <param name="position">Rectangle on the screen to use for the property GUI</param>
        /// <param name="property">The SerializedProperty to make the custom GUI for</param>
        /// <param name="label">The label to display for this property</param>
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            var underlyingProperty = property.FindPropertyRelative(UnderlyingValueField);
            var args = GetCachedArguments(fieldInfo);

            EditorGUI.BeginProperty(position, label, property);

            var assignedObject = EditorGUI.ObjectField(position, label,
                underlyingProperty.objectReferenceValue, args.ObjectType, true);

            ProcessAssignedObject(underlyingProperty, assignedObject, args);

            EditorGUI.EndProperty();
            InterfaceReferenceUtil.OnGUI(position, underlyingProperty, label, args);
        }

        /// <summary>
        /// Processes the assigned object and updates the property value.
        /// </summary>
        /// <param name="property">The property to update</param>
        /// <param name="assignedObject">The object assigned in the inspector</param>
        /// <param name="args">Interface arguments containing type information</param>
        private void ProcessAssignedObject(SerializedProperty property, Object assignedObject, InterfaceArgs args)
        {
            if (assignedObject == null)
            {
                property.objectReferenceValue = null;
                return;
            }

            var component = GetValidComponent(assignedObject, args.InterfaceType);
            if (component != null)
            {
                ValidateAndAssignObject(property, component, component.name, args.InterfaceType.Name);
            }
            else
            {
                Debug.LogWarning($"Assigned object does not implement required interface '{args.InterfaceType.Name}'.");
                property.objectReferenceValue = null;
            }
        }

        /// <summary>
        /// Gets a valid component that implements the interface from the assigned object.
        /// </summary>
        /// <param name="assignedObject">The object assigned in the inspector</param>
        /// <param name="interfaceType">The interface type to check for</param>
        /// <returns>A valid component or null if none found</returns>
        private static Object GetValidComponent(Object assignedObject, Type interfaceType)
        {
            if (assignedObject is GameObject gameObject)
            {
                return gameObject.GetComponent(interfaceType);
            }

            return interfaceType.IsAssignableFrom(assignedObject.GetType()) ? assignedObject : null;
        }

        /// <summary>
        /// Gets cached interface arguments for the field.
        /// </summary>
        /// <param name="fieldInfo">Field info to get arguments for</param>
        /// <returns>Interface arguments containing type information</returns>
        private InterfaceArgs GetCachedArguments(FieldInfo fieldInfo)
        {
            var fieldType = fieldInfo.FieldType;
            if (typeCache.TryGetValue(fieldType, out var cachedArgs))
            {
                return cachedArgs;
            }

            var args = GetArguments(fieldInfo);
            typeCache[fieldType] = args;
            return args;
        }

        /// <summary>
        /// Gets interface arguments for the field.
        /// </summary>
        /// <param name="fieldInfo">Field info to get arguments for</param>
        /// <returns>Interface arguments containing type information</returns>
        private static InterfaceArgs GetArguments(FieldInfo fieldInfo)
        {
            var fieldType = fieldInfo.FieldType;
            if (TryGetTypesFromInterfaceReference(fieldType, out var objType, out var interfaceType))
            {
                return new InterfaceArgs(objType, interfaceType);
            }

            GetTypesFromList(fieldType, out objType, out interfaceType);
            return new InterfaceArgs(objType, interfaceType);
        }

        /// <summary>
        /// Tries to get object and interface types from an InterfaceReference type.
        /// </summary>
        /// <param name="type">The type to check</param>
        /// <param name="objType">Output object type</param>
        /// <param name="interfaceType">Output interface type</param>
        /// <returns>True if types were successfully extracted</returns>
        private static bool TryGetTypesFromInterfaceReference(Type type, out Type objType, out Type interfaceType)
        {
            objType = interfaceType = null;

            if (type?.IsGenericType != true) return false;

            var genericType = type.GetGenericTypeDefinition();
            if (genericType == typeof(InterfaceReference<>))
                type = type.BaseType;

            if (type?.GetGenericTypeDefinition() != typeof(InterfaceReference<,>))
                return false;

            var types = type.GetGenericArguments();
            interfaceType = types[0];
            objType = types[1];
            return true;
        }

        /// <summary>
        /// Gets object and interface types from a list type.
        /// </summary>
        /// <param name="type">The type to check</param>
        /// <param name="objType">Output object type</param>
        /// <param name="interfaceType">Output interface type</param>
        private static void GetTypesFromList(Type type, out Type objType, out Type interfaceType)
        {
            objType = interfaceType = null;

            var listInterface = type.GetInterfaces()
                .FirstOrDefault(x => x.IsGenericType && x.GetGenericTypeDefinition() == typeof(IList<>));

            if (listInterface != null)
            {
                var elementType = listInterface.GetGenericArguments()[0];
                TryGetTypesFromInterfaceReference(elementType, out objType, out interfaceType);
            }
        }

        /// <summary>
        /// Validates and assigns an object to a property.
        /// </summary>
        /// <param name="property">The property to update</param>
        /// <param name="targetObject">The target object to assign</param>
        /// <param name="componentNameOrType">Name of the component or type</param>
        /// <param name="interfaceName">Name of the interface</param>
        private static void ValidateAndAssignObject(SerializedProperty property, Object targetObject,
            string componentNameOrType, string interfaceName = null)
        {
            if (targetObject != null)
            {
                property.objectReferenceValue = targetObject;
                return;
            }

            var message = interfaceName != null
                ? $"GameObject '{componentNameOrType}'"
                : "assigned object";

            Debug.LogWarning(
                $"The {message} does not have a component that implements '{interfaceName}'."
            );
            property.objectReferenceValue = null;
        }
    }
}