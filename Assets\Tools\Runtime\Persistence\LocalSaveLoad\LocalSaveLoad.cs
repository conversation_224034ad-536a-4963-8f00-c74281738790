using UnityEngine;
using System;
using System.Text;
using System.Security.Cryptography;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Provides device-specific encrypted save/load functionality using Unity's PlayerPrefs.
    /// </summary>
    public static class LocalSaveLoad
    {
        private static string deviceSpecificPassword;

        private static string DeviceSpecificPassword
        {
            get
            {
                if (string.IsNullOrEmpty(deviceSpecificPassword))
                {
                    deviceSpecificPassword = GenerateDeviceSpecificPassword();
                }
                return deviceSpecificPassword;
            }
        }

        private static string GenerateDeviceSpecificPassword()
        {
            var deviceDataBuilder = new StringBuilder();
            deviceDataBuilder.Append(SystemInfo.deviceUniqueIdentifier);
            deviceDataBuilder.Append(SystemInfo.deviceModel);
            deviceDataBuilder.Append(SystemInfo.deviceName);
            deviceDataBuilder.Append(SystemInfo.processorType);
            deviceDataBuilder.Append(SystemInfo.graphicsDeviceName);

            var installationId = PlayerPrefs.GetString("InstallationId", string.Empty);
            if (string.IsNullOrEmpty(installationId))
            {
                installationId = Guid.NewGuid().ToString();
                PlayerPrefs.SetString("InstallationId", installationId);
                PlayerPrefs.Save();
            }
            deviceDataBuilder.Append(installationId);

            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(deviceDataBuilder.ToString()));
                return Convert.ToBase64String(hashBytes);
            }
        }

        /// <summary>
        /// Saves data of type T to PlayerPrefs with encryption.
        /// </summary>
        /// <typeparam name="T">Type of data to save.</typeparam>
        /// <param name="key">Key to identify the data.</param>
        /// <param name="data">Data to save.</param>
        public static void Save<T>(string key, T data)
        {
            try
            {
                var jsonString = JsonUtility.ToJson(data);
                var encryptedJson = StringEncryption.EncryptString(jsonString, DeviceSpecificPassword);
                PlayerPrefs.SetString(key, encryptedJson);
                PlayerPrefs.Save();
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to save data for key '{key}': {ex.Message}");
            }
        }

        /// <summary>
        /// Loads data of type T from PlayerPrefs with decryption.
        /// </summary>
        /// <typeparam name="T">Type of data to load.</typeparam>
        /// <param name="key">Key to identify the data.</param>
        /// <param name="defaultValue">Default value if key does not exist or fails to load.</param>
        /// <returns>Loaded data or default value.</returns>
        public static T Load<T>(string key, T defaultValue = default)
        {
            try
            {
                if (!PlayerPrefs.HasKey(key))
                {
                    return defaultValue;
                }

                var encryptedJson = PlayerPrefs.GetString(key);
                var decryptedJson = StringEncryption.DecryptString(encryptedJson, DeviceSpecificPassword);
                return JsonUtility.FromJson<T>(decryptedJson);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to load data for key '{key}': {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// Deletes the data associated with the specified key.
        /// </summary>
        /// <param name="key">Key to delete.</param>
        public static void Delete(string key)
        {
            PlayerPrefs.DeleteKey(key);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// Deletes all saved data.
        /// </summary>
        public static void DeleteAll()
        {
            PlayerPrefs.DeleteAll();
            PlayerPrefs.Save();
        }

        /// <summary>
        /// Checks if data exists for the specified key.
        /// </summary>
        /// <param name="key">Key to check.</param>
        /// <returns>True if data exists, otherwise false.</returns>
        public static bool Has(string key)
        {
            return PlayerPrefs.HasKey(key);
        }

        /// <summary>
        /// Regenerates the device-specific password by resetting the installation ID.
        /// </summary>
        public static void RegenerateDevicePassword()
        {
            PlayerPrefs.DeleteKey("InstallationId");
            PlayerPrefs.Save();
            deviceSpecificPassword = null;
        }
    }
}
