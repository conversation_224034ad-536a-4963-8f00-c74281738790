using System.Collections;

namespace Game.CommandSystem
{
    /// <summary>
    /// Interface for commands that can be executed as coroutines.
    /// </summary>
    public interface ICoroutineCommand
    {
        /// <summary>
        /// Executes the command's logic. Designed to be run as a coroutine
        /// to handle actions that occur over time.
        /// </summary>
        /// <returns>An enumerator that can be used to execute the command over time.</returns>
        IEnumerator Execute();
    }
}