using System;
using System.Collections.Generic;
using System.Reflection;
using SmartVertex.Tools;
using UnityEditor;
using UnityEngine;

namespace SmartVertex.EditorTools
{
    /// <summary>
    /// Editor window for debugging and inspecting event subscriptions in the EventBus.
    /// </summary>
    public class EventBusWindow : BaseDebugWindow
    {
        private readonly Dictionary<Type, bool> foldoutStates = new();

        /// <summary>
        /// Shows the Event Bus window.
        /// </summary>
        public static void ShowWindow()
        {
            var window = GetWindow<EventBusWindow>("Event Bus");
            window.minSize = new Vector2(400, 300);
        }

        /// <inheritdoc/>
        protected override void OnEnable()
        {
            base.OnEnable();
            refreshIcon.tooltip = "Refresh event subscriptions";
            clearIcon.tooltip = "Clear all subscriptions";
        }

        private void OnGUI()
        {
            InitializeStyles();
            EditorGUILayout.BeginVertical();
            // Header
            EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
            GUILayout.Label("Event Bus Debugger", headerStyle);
            GUILayout.FlexibleSpace();
            if (GUILayout.Button(refreshIcon, EditorStyles.toolbarButton))
            {
                OnRefreshClicked();
            }
            if (GUILayout.Button(clearIcon, EditorStyles.toolbarButton))
            {
                OnClearClicked();
            }
            EditorGUILayout.EndHorizontal();
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            DisplayEventSubscriptions();
            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();
        }

        /// <inheritdoc/>
        protected override void OnRefreshClicked()
        {
            foldoutStates.Clear();
            Repaint();
        }

        /// <inheritdoc/>
        protected override void OnClearClicked()
        {
            if (DisplayConfirmationDialog("Clear All Subscriptions",
                "Are you sure you want to clear all event subscriptions?\nThis could break functionality in your application.",
                "Clear", "Cancel"))
            {
                ClearAllSubscriptions();
            }
        }

        private void DisplayEventSubscriptions()
        {
            Dictionary<Type, List<Delegate>> subscriptions = GetEventSubscriptions();
            if (subscriptions == null || subscriptions.Count == 0)
            {
                EditorGUILayout.HelpBox("No event subscriptions are currently registered.", MessageType.Info);
                return;
            }
            EditorGUILayout.LabelField($"Event Types: {subscriptions.Count}", subHeaderStyle);
            EditorGUILayout.Space();
            Dictionary<Type, List<Delegate>> subscriptionsCopy = new();
            foreach (var kvp in subscriptions)
            {
                subscriptionsCopy[kvp.Key] = new List<Delegate>(kvp.Value);
            }
            foreach (var kvp in subscriptionsCopy)
            {
                Type eventType = kvp.Key;
                List<Delegate> handlers = kvp.Value;
                if (!foldoutStates.ContainsKey(eventType))
                {
                    foldoutStates[eventType] = false;
                }
                EditorGUILayout.BeginVertical(boxStyle);
                EditorGUILayout.BeginHorizontal();
                foldoutStates[eventType] = EditorGUILayout.Foldout(foldoutStates[eventType],
                    $"{eventType.Name} ({handlers.Count} {(handlers.Count == 1 ? "subscriber" : "subscribers")})",
                    true, foldoutStyle);
                bool shouldClear = false;
                if (GUILayout.Button("Clear", GUILayout.Width(60)))
                {
                    if (EditorUtility.DisplayDialog("Clear Event Subscriptions",
                        $"Are you sure you want to clear all subscriptions for {eventType.Name}?",
                        "Clear", "Cancel"))
                    {
                        shouldClear = true;
                    }
                }
                EditorGUILayout.EndHorizontal();
                if (foldoutStates[eventType])
                {
                    EditorGUI.indentLevel++;
                    for (int i = 0; i < handlers.Count; i++)
                    {
                        Delegate handler = handlers[i];
                        DisplaySubscriber(i, handler);
                    }
                    EditorGUI.indentLevel--;
                }
                EditorGUILayout.EndVertical();
                EditorGUILayout.Space(5);
                if (shouldClear)
                {
                    ClearSubscriptionsForType(eventType);
                    continue;
                }
            }
        }

        private void DisplaySubscriber(int index, Delegate handler)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField($"Subscriber {index + 1}:", EditorStyles.boldLabel);
            MethodInfo method = handler.Method;
            EditorGUILayout.LabelField("Method:", method.Name);
            if (handler.Target != null)
            {
                Type targetType = handler.Target.GetType();
                EditorGUILayout.LabelField("Target Type:", targetType.FullName);
                if (handler.Target is UnityEngine.Object unityObject)
                {
                    EditorGUILayout.ObjectField("Target Object:", unityObject, targetType, true);
                }
            }
            else
            {
                EditorGUILayout.LabelField("Target:", "Static Method");
            }
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(2);
        }

        private Dictionary<Type, List<Delegate>> GetEventSubscriptions()
        {
            Type eventBusType = typeof(EventBus);
            FieldInfo subscriptionsField = eventBusType.GetField("eventSubscriptions", BindingFlags.NonPublic | BindingFlags.Static);
            if (subscriptionsField != null)
            {
                return subscriptionsField.GetValue(null) as Dictionary<Type, List<Delegate>>;
            }
            return new Dictionary<Type, List<Delegate>>();
        }

        private void ClearSubscriptionsForType(Type eventType)
        {
            MethodInfo genericMethod = typeof(EventBus).GetMethod("ClearSubscriptions");
            MethodInfo typedMethod = genericMethod.MakeGenericMethod(eventType);
            typedMethod.Invoke(null, null);
            Repaint();
        }

        private void ClearAllSubscriptions()
        {
            MethodInfo clearMethod = typeof(EventBus).GetMethod("ClearAllSubscriptions");
            clearMethod.Invoke(null, null);
            Repaint();
        }
    }
}
