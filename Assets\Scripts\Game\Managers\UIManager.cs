using SmartVertex.Tools;
using TMPro;
using UnityEngine;

namespace Game.Managers
{
    /// <summary>
    /// Manages UI elements and subtitle display in the game.
    /// </summary>
    public class UIManager : Singleton<UIManager>
    {
        [SerializeField] private TextMeshProUGUI subtitleText;

        /// <summary>
        /// Gets or sets the color of the subtitle text.
        /// </summary>
        public Color SubtitleColor
        {
            get => SubtitleText.color;
            set => SubtitleText.color = value;
        }

        /// <summary>
        /// Gets or sets the font size of the subtitle text.
        /// </summary>
        public float SubtitleFontSize
        {
            get => SubtitleText.fontSize;
            set => SubtitleText.fontSize = value;
        }

        /// <summary>
        /// Gets the subtitle TextMeshProUGUI component.
        /// </summary>
        private TextMeshProUGUI SubtitleText => subtitleText;

        /// <summary>
        /// Shows the subtitle with the specified text.
        /// </summary>
        /// <param name="text">The subtitle text to display.</param>
        public void ShowSubtitle(string text)
        {
            SubtitleText.text = text;
            SubtitleText.gameObject.SetActive(true);
        }
    }
}