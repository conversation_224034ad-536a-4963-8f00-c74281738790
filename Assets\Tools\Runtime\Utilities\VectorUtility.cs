using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Provides optimized utility methods for generating random vectors.
    /// </summary>
    public static class VectorUtility
    {
        #region Vector2 Random Generation

        /// <summary>
        /// Generates a random Vector2 with values between -1 and 1.
        /// </summary>
        /// <returns>A random Vector2 with x and y values between -1 and 1.</returns>
        public static Vector2 RandomVector2()
        {
            return new Vector2(
                Random.Range(-1f, 1f),
                Random.Range(-1f, 1f)
            );
        }

        /// <summary>
        /// Generates a random Vector2 within specified bounds.
        /// </summary>
        /// <param name="min">Minimum values for x and y components.</param>
        /// <param name="max">Maximum values for x and y components.</param>
        /// <returns>A random Vector2 within the specified bounds.</returns>
        public static Vector2 RandomVector2(Vector2 min, Vector2 max)
        {
            return new Vector2(
                Random.Range(min.x, max.x),
                Random.Range(min.y, max.y)
            );
        }

        /// <summary>
        /// Generates a random Vector2 with the specified magnitude.
        /// </summary>
        /// <param name="magnitude">The desired magnitude of the vector.</param>
        /// <returns>A random Vector2 with the specified magnitude.</returns>
        public static Vector2 RandomVector2WithMagnitude(float magnitude)
        {
            float angle = Random.Range(0f, 2f * Mathf.PI);
            return new Vector2(
                Mathf.Cos(angle) * magnitude,
                Mathf.Sin(angle) * magnitude
            );
        }

        /// <summary>
        /// Generates a random normalized Vector2 (unit vector).
        /// </summary>
        /// <returns>A random unit Vector2.</returns>
        public static Vector2 RandomUnitVector2()
        {
            float angle = Random.Range(0f, 2f * Mathf.PI);
            return new Vector2(Mathf.Cos(angle), Mathf.Sin(angle));
        }

        #endregion

        #region Vector3 Random Generation

        /// <summary>
        /// Generates a random Vector3 with values between -1 and 1.
        /// </summary>
        /// <returns>A random Vector3 with x, y, and z values between -1 and 1.</returns>
        public static Vector3 RandomVector3()
        {
            return new Vector3(
                Random.Range(-1f, 1f),
                Random.Range(-1f, 1f),
                Random.Range(-1f, 1f)
            );
        }

        /// <summary>
        /// Generates a random Vector3 within specified bounds.
        /// </summary>
        /// <param name="min">Minimum values for x, y, and z components.</param>
        /// <param name="max">Maximum values for x, y, and z components.</param>
        /// <returns>A random Vector3 within the specified bounds.</returns>
        public static Vector3 RandomVector3(Vector3 min, Vector3 max)
        {
            return new Vector3(
                Random.Range(min.x, max.x),
                Random.Range(min.y, max.y),
                Random.Range(min.z, max.z)
            );
        }

        /// <summary>
        /// Generates a random Vector3 with the specified magnitude.
        /// </summary>
        /// <param name="magnitude">The desired magnitude of the vector.</param>
        /// <returns>A random Vector3 with the specified magnitude.</returns>
        public static Vector3 RandomVector3WithMagnitude(float magnitude)
        {
            Vector3 vector = RandomUnitVector3();
            return vector * magnitude;
        }

        /// <summary>
        /// Generates a random normalized Vector3 (unit vector).
        /// </summary>
        /// <returns>A random unit Vector3.</returns>
        public static Vector3 RandomUnitVector3()
        {
            // Using spherical coordinates for uniform distribution
            float theta = Random.Range(0f, 2f * Mathf.PI);
            float phi = Mathf.Acos(Random.Range(-1f, 1f));

            float sinPhi = Mathf.Sin(phi);
            return new Vector3(
                sinPhi * Mathf.Cos(theta),
                sinPhi * Mathf.Sin(theta),
                Mathf.Cos(phi)
            );
        }

        #endregion

        #region Constrained Random Generation

        /// <summary>
        /// Generates a random Vector2 within the range defined by two vectors.
        /// </summary>
        /// <param name="bounds1">First boundary vector.</param>
        /// <param name="bounds2">Second boundary vector.</param>
        /// <returns>A random Vector2 within the rectangular area defined by the bounds.</returns>
        public static Vector2 RandomVector2InRange(Vector2 bounds1, Vector2 bounds2)
        {
            return new Vector2(
                Random.Range(Mathf.Min(bounds1.x, bounds2.x), Mathf.Max(bounds1.x, bounds2.x)),
                Random.Range(Mathf.Min(bounds1.y, bounds2.y), Mathf.Max(bounds1.y, bounds2.y))
            );
        }

        /// <summary>
        /// Generates a random Vector3 within the range defined by two vectors.
        /// </summary>
        /// <param name="bounds1">First boundary vector.</param>
        /// <param name="bounds2">Second boundary vector.</param>
        /// <returns>A random Vector3 within the box defined by the bounds.</returns>
        public static Vector3 RandomVector3InRange(Vector3 bounds1, Vector3 bounds2)
        {
            return new Vector3(
                Random.Range(Mathf.Min(bounds1.x, bounds2.x), Mathf.Max(bounds1.x, bounds2.x)),
                Random.Range(Mathf.Min(bounds1.y, bounds2.y), Mathf.Max(bounds1.y, bounds2.y)),
                Random.Range(Mathf.Min(bounds1.z, bounds2.z), Mathf.Max(bounds1.z, bounds2.z))
            );
        }

        /// <summary>
        /// Generates a random Vector2 between two points.
        /// </summary>
        /// <param name="start">Starting point.</param>
        /// <param name="end">Ending point.</param>
        /// <returns>A random Vector2 along the line between start and end.</returns>
        public static Vector2 RandomVector2Between(Vector2 start, Vector2 end)
        {
            float t = Random.Range(0f, 1f);
            return Vector2.Lerp(start, end, t);
        }

        /// <summary>
        /// Generates a random Vector3 between two points.
        /// </summary>
        /// <param name="start">Starting point.</param>
        /// <param name="end">Ending point.</param>
        /// <returns>A random Vector3 along the line between start and end.</returns>
        public static Vector3 RandomVector3Between(Vector3 start, Vector3 end)
        {
            float t = Random.Range(0f, 1f);
            return Vector3.Lerp(start, end, t);
        }

        /// <summary>
        /// Generates a random point inside a circle.
        /// </summary>
        /// <param name="center">Center of the circle.</param>
        /// <param name="radius">Radius of the circle.</param>
        /// <returns>A random point inside the circle.</returns>
        public static Vector2 RandomInCircle(Vector2 center, float radius)
        {
            // Using square root for uniform distribution
            float angle = Random.Range(0f, 2f * Mathf.PI);
            float r = Mathf.Sqrt(Random.Range(0f, 1f)) * radius;
            return center + new Vector2(Mathf.Cos(angle) * r, Mathf.Sin(angle) * r);
        }

        /// <summary>
        /// Generates a random point inside a circle centered at origin.
        /// </summary>
        /// <param name="radius">Radius of the circle.</param>
        /// <returns>A random point inside the circle.</returns>
        public static Vector2 RandomInCircle(float radius = 1f)
        {
            float angle = Random.Range(0f, 2f * Mathf.PI);
            float r = Mathf.Sqrt(Random.Range(0f, 1f)) * radius;
            return new Vector2(Mathf.Cos(angle) * r, Mathf.Sin(angle) * r);
        }

        /// <summary>
        /// Generates a random point on the perimeter of a circle.
        /// </summary>
        /// <param name="center">Center of the circle.</param>
        /// <param name="radius">Radius of the circle.</param>
        /// <returns>A random point on the circle's perimeter.</returns>
        public static Vector2 RandomOnCircle(Vector2 center, float radius)
        {
            float angle = Random.Range(0f, 2f * Mathf.PI);
            return center + new Vector2(Mathf.Cos(angle) * radius, Mathf.Sin(angle) * radius);
        }

        /// <summary>
        /// Generates a random point on the perimeter of a circle centered at origin.
        /// </summary>
        /// <param name="radius">Radius of the circle.</param>
        /// <returns>A random point on the circle's perimeter.</returns>
        public static Vector2 RandomOnCircle(float radius = 1f)
        {
            float angle = Random.Range(0f, 2f * Mathf.PI);
            return new Vector2(Mathf.Cos(angle) * radius, Mathf.Sin(angle) * radius);
        }

        /// <summary>
        /// Generates a random point inside a sphere.
        /// </summary>
        /// <param name="center">Center of the sphere.</param>
        /// <param name="radius">Radius of the sphere.</param>
        /// <returns>A random point inside the sphere.</returns>
        public static Vector3 RandomInSphere(Vector3 center, float radius)
        {
            // Using cube root for uniform distribution
            float u = Random.Range(0f, 1f);
            float r = Mathf.Pow(u, 1f / 3f) * radius;
            Vector3 direction = RandomUnitVector3();
            return center + direction * r;
        }

        /// <summary>
        /// Generates a random point inside a sphere centered at origin.
        /// </summary>
        /// <param name="radius">Radius of the sphere.</param>
        /// <returns>A random point inside the sphere.</returns>
        public static Vector3 RandomInSphere(float radius = 1f)
        {
            float u = Random.Range(0f, 1f);
            float r = Mathf.Pow(u, 1f / 3f) * radius;
            Vector3 direction = RandomUnitVector3();
            return direction * r;
        }

        /// <summary>
        /// Generates a random point on the surface of a sphere.
        /// </summary>
        /// <param name="center">Center of the sphere.</param>
        /// <param name="radius">Radius of the sphere.</param>
        /// <returns>A random point on the sphere's surface.</returns>
        public static Vector3 RandomOnSphere(Vector3 center, float radius)
        {
            Vector3 direction = RandomUnitVector3();
            return center + direction * radius;
        }

        /// <summary>
        /// Generates a random point on the surface of a sphere centered at origin.
        /// </summary>
        /// <param name="radius">Radius of the sphere.</param>
        /// <returns>A random point on the sphere's surface.</returns>
        public static Vector3 RandomOnSphere(float radius = 1f)
        {
            Vector3 direction = RandomUnitVector3();
            return direction * radius;
        }

        #endregion
    }
}
