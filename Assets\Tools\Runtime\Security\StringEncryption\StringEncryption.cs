using System;
using System.IO;
using System.Text;
using System.Security.Cryptography;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Provides methods for encrypting and decrypting strings using AES with password-based key derivation.
    /// </summary>
    public static class StringEncryption
    {
        /// <summary>
        /// Gets the key size in bits.
        /// </summary>
        public static int KeySize => 128;

        /// <summary>
        /// Gets the salt size in bytes.
        /// </summary>
        public static int SaltSize => 16;

        /// <summary>
        /// Gets the IV size in bytes.
        /// </summary>
        public static int IvSize => 16;

        /// <summary>
        /// Gets the number of iterations for key derivation.
        /// </summary>
        public static int Iterations => 5000;

        /// <summary>
        /// Encrypts the specified plain text using the provided password.
        /// </summary>
        /// <param name="plainText">The string to encrypt.</param>
        /// <param name="password">The password for encryption.</param>
        /// <returns>The encrypted string, encoded in Base64.</returns>
        /// <exception cref="CryptographicException">Thrown if encryption fails.</exception>
        public static string EncryptString(string plainText, string password)
        {
            try
            {
                byte[] salt = new byte[SaltSize];
                using (var rng = RandomNumberGenerator.Create())
                {
                    rng.GetBytes(salt);
                }

                byte[] key;
                using (var keyDerivation = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithmName.SHA256))
                {
                    key = keyDerivation.GetBytes(KeySize / 8);
                }

                byte[] iv = new byte[IvSize];
                using (var rng = RandomNumberGenerator.Create())
                {
                    rng.GetBytes(iv);
                }

                byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);

                using var aes = Aes.Create();
                aes.Key = key;
                aes.IV = iv;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;

                using var memoryStream = new MemoryStream();
                memoryStream.Write(salt, 0, salt.Length);
                memoryStream.Write(iv, 0, iv.Length);

                using (var cryptoStream = new CryptoStream(memoryStream, aes.CreateEncryptor(), CryptoStreamMode.Write))
                {
                    cryptoStream.Write(plainBytes, 0, plainBytes.Length);
                    cryptoStream.FlushFinalBlock();
                }

                return Convert.ToBase64String(memoryStream.ToArray());
            }
            catch (Exception ex)
            {
                throw new CryptographicException("Encryption failed", ex);
            }
        }

        /// <summary>
        /// Decrypts the specified encrypted string using the provided password.
        /// </summary>
        /// <param name="encryptedData">The encrypted string, encoded in Base64.</param>
        /// <param name="password">The password for decryption.</param>
        /// <returns>The decrypted plain text string.</returns>
        /// <exception cref="CryptographicException">Thrown if decryption fails.</exception>
        public static string DecryptString(string encryptedData, string password)
        {
            try
            {
                byte[] encryptedBytes = Convert.FromBase64String(encryptedData);

                byte[] salt = new byte[SaltSize];
                byte[] iv = new byte[IvSize];
                Buffer.BlockCopy(encryptedBytes, 0, salt, 0, SaltSize);
                Buffer.BlockCopy(encryptedBytes, SaltSize, iv, 0, IvSize);

                byte[] key;
                using (var keyDerivation = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithmName.SHA256))
                {
                    key = keyDerivation.GetBytes(KeySize / 8);
                }

                using var aes = Aes.Create();
                aes.Key = key;
                aes.IV = iv;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;

                int cipherTextOffset = SaltSize + IvSize;
                int cipherTextLength = encryptedBytes.Length - cipherTextOffset;

                using var inputStream = new MemoryStream(encryptedBytes, cipherTextOffset, cipherTextLength);
                using var cryptoStream = new CryptoStream(inputStream, aes.CreateDecryptor(), CryptoStreamMode.Read);
                using var reader = new StreamReader(cryptoStream, Encoding.UTF8);
                return reader.ReadToEnd();
            }
            catch (Exception ex)
            {
                throw new CryptographicException("Decryption failed", ex);
            }
        }
    }
}