using System;
using System.Collections;
using Game.Interfaces;
using Game.Managers;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to play an animation on a target object.
    /// </summary>
    public class PlayAnimationCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the play animation command.</summary>
        public PlayAnimationParams Parameters { get; }

        /// <summary>Creates a new PlayAnimationCommand.</summary>
        /// <param name="parameters">Play animation parameters.</param>
        public PlayAnimationCommand(PlayAnimationParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            GameObject targetObject = ObjectManager.Instance.FindById(Parameters.targetId);
            IAnimateable animateable = targetObject.GetComponentInChildren<IAnimateable>();
            bool isDone = false;
            animateable.PlayAnimation(Parameters.animationName, () => { isDone = true; });

            yield return new WaitUntil(() => isDone == true);
        }

        public static int FindAnimationLayer(Animator animator, string animationName)
        {
            if (animator.runtimeAnimatorController == null)
                return -1;

            // Check each layer for the animation state
            for (int layerIndex = 0; layerIndex < animator.layerCount; layerIndex++)
            {
                // Check if the animation state exists in this layer
                if (animator.HasState(layerIndex, Animator.StringToHash(animationName)))
                {
                    return layerIndex;
                }
            }

            return -1; // Animation not found in any layer
        }
    }

    /// <summary>Parameters for PlayAnimation command.</summary>
    [Serializable]
    public struct PlayAnimationParams
    {
        public string targetId;
        public string animationName;
        public float duration;
    }
}
