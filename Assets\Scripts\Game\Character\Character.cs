using System.Collections;
using Game.CommandSystem;
using Game.Interfaces;
using UnityEngine;
using UnityEngine.Events;

namespace Game.CharacterSystem
{
    /// <summary>
    /// Represents a character in the game with animation and dialogue capabilities.
    /// </summary>
    public class Character : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IAnimateable
    {
        [SerializeField] private Animator animator;
        [SerializeField] private AudioSource dialogueAudioSource;
        [SerializeField] private string voiceInstructions;
        [SerializeField] private string voiceActor;
        [SerializeField] private float voiceSpeed = 1.0f;

        private string characterId;

        /// <summary>
        /// Animator component for this character.
        /// </summary>
        public Animator Animator => animator;

        /// <summary>
        /// AudioSource used for dialogue playback.
        /// </summary>
        public AudioSource DialogueAudioSource => dialogueAudioSource;

        /// <summary>
        /// voice instructions for this character.
        /// </summary>
        public string VoiceInstructions => voiceInstructions;

        /// <summary>
        /// name of the voice actor for this character.
        /// </summary>
        public string VoiceActor => voiceActor;

        /// <summary>
        /// Speed of the voice playback for this character.
        /// </summary>
        public float VoiceSpeed => voiceSpeed;

        /// <summary>
        /// Unique identifier for this character.
        /// </summary>
        public string CharacterId
        {
            get => characterId;
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    Debug.LogError("Character ID cannot be null or empty.");
                    return;
                }
                characterId = value;
            }
        }

        public void PlayDialogue(AudioClip audio)
        {
            DialogueAudioSource.clip = audio;
            DialogueAudioSource.Play();
        }

        public void StopDialogue()
        {
            if (DialogueAudioSource.isPlaying)
            {
                DialogueAudioSource.Stop();
            }
        }

        public void PlayAnimation(string animationName, UnityAction onAnimationComplete = null)
        {
            if (animator == null)
            {
                Debug.LogError("Animator component is not assigned.");
                return;
            }

            // Find the layer that contains this animation state
            int targetLayer = PlayAnimationCommand.FindAnimationLayer(animator, animationName);
            if (targetLayer == -1)
            {
                Debug.LogError($"Animation '{animationName}' not found in any layer of the Animator Controller.");
                return;
            }

            animator.Play(animationName, targetLayer);

            if (onAnimationComplete != null)
            {
                StartCoroutine(InvokeAfterAnimationDuration(onAnimationComplete, targetLayer));
            }
        }

        private IEnumerator InvokeAfterAnimationDuration(UnityAction callback, int layerIndex)
        {
            // Wait one frame for the animation to start
            yield return null;

            // Get the current animation state info for the layer containing our animation
            AnimatorStateInfo stateInfo = animator.GetCurrentAnimatorStateInfo(layerIndex);

            // Calculate the actual duration based on clip length and speed
            float animationDuration = stateInfo.length / stateInfo.speed;

            // Wait for the calculated duration
            yield return new WaitForSeconds(animationDuration);

            // Invoke the callback
            callback?.Invoke();
        }

        private void Awake()
        {
            dialogueAudioSource.loop = false;
            dialogueAudioSource.playOnAwake = false;
        }
    }
}