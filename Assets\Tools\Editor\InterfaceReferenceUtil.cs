using System;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace SmartVertex.EditorTools
{
    /// <summary>
    /// Utility class for interface reference drawing in the inspector.
    /// </summary>
    public static class InterfaceReferenceUtil
    {
        private static GUIStyle labelStyle;
        private const int AdditionalLeftWidth = 3;
        private const int VerticalIndent = 1;

        /// <summary>
        /// Draws the interface reference GUI elements.
        /// </summary>
        /// <param name="position">Rectangle on the screen to use for the GUI</param>
        /// <param name="property">The SerializedProperty to draw for</param>
        /// <param name="label">The label to display (not used but kept for API compatibility)</param>
        /// <param name="args">Interface arguments containing type information</param>
        public static void OnGUI(Rect position, SerializedProperty property, GUIContent label, InterfaceArgs args)
        {
            InitializeStyleIfNeeded();
            var controlId = GUIUtility.GetControlID(FocusType.Passive) - 1;
            var displayString = ShouldShowInterfaceName(position, property)
                ? $"({args.InterfaceType.Name})"
                : "*";
            DrawInterfaceNameLabel(position, displayString, controlId);
        }

        /// <summary>
        /// Determines whether to show the interface name.
        /// </summary>
        /// <param name="position">Rectangle on the screen</param>
        /// <param name="property">The SerializedProperty to check</param>
        /// <returns>True if the interface name should be shown</returns>
        private static bool ShouldShowInterfaceName(Rect position, SerializedProperty property) =>
            property.objectReferenceValue == null || position.Contains(Event.current.mousePosition);

        /// <summary>
        /// Draws the interface name label.
        /// </summary>
        /// <param name="position">Rectangle on the screen to use for the GUI</param>
        /// <param name="displayString">The string to display</param>
        /// <param name="controlId">The control ID</param>
        private static void DrawInterfaceNameLabel(Rect position, string displayString, int controlId)
        {
            if (Event.current.type != EventType.Repaint) return;
            var content = EditorGUIUtility.TrTextContent(displayString);
            var labelPos = CalculateLabelPosition(position, content);
            var isActiveDrag = DragAndDrop.activeControlID == controlId;
            labelStyle.Draw(labelPos, content, controlId, isActiveDrag, false);
        }

        /// <summary>
        /// Calculates the position for the label.
        /// </summary>
        /// <param name="position">Rectangle on the screen</param>
        /// <param name="content">The content to display</param>
        /// <returns>The calculated rectangle position</returns>
        private static Rect CalculateLabelPosition(Rect position, GUIContent content)
        {
            var size = labelStyle.CalcSize(content);
            return new Rect(
                position.width - size.x - AdditionalLeftWidth - 18 + position.x,
                position.y + VerticalIndent,
                size.x + AdditionalLeftWidth,
                position.height - VerticalIndent * 2
            );
        }

        /// <summary>
        /// Initializes the label style if needed.
        /// </summary>
        private static void InitializeStyleIfNeeded()
        {
            if (labelStyle != null) return;
            labelStyle = new GUIStyle(EditorStyles.label)
            {
                font = EditorStyles.objectField.font,
                fontSize = EditorStyles.objectField.fontSize,
                fontStyle = EditorStyles.objectField.fontStyle,
                alignment = TextAnchor.MiddleRight,
                padding = new RectOffset(0, 2, 0, 0)
            };
        }
    }

    /// <summary>
    /// Structure containing interface and object type information.
    /// </summary>
    public readonly struct InterfaceArgs
    {
        /// <summary>
        /// The Unity Object type.
        /// </summary>
        public readonly Type ObjectType;
        /// <summary>
        /// The interface type.
        /// </summary>
        public readonly Type InterfaceType;
        /// <summary>
        /// Creates a new InterfaceArgs instance.
        /// </summary>
        /// <param name="objectType">The Unity Object type</param>
        /// <param name="interfaceType">The interface type</param>
        public InterfaceArgs(Type objectType, Type interfaceType)
        {
            Debug.Assert(typeof(Object).IsAssignableFrom(objectType),
                $"{nameof(objectType)} needs to be of Type {typeof(Object)}.");
            Debug.Assert(interfaceType.IsInterface,
                $"{nameof(interfaceType)} needs to be an interface.");
            ObjectType = objectType;
            InterfaceType = interfaceType;
        }
    }
}