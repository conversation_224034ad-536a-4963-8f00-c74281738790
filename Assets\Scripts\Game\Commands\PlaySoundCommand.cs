using System;
using System.Collections;
using Game.Managers;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to play a sound effect.
    /// </summary>
    public class PlaySoundCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the play sound command.</summary>
        public PlaySoundParams Parameters { get; }

        /// <summary>Creates a new PlaySoundCommand.</summary>
        /// <param name="parameters">Play sound parameters.</param>
        public PlaySoundCommand(PlaySoundParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            if (Parameters.isMusic)
            {
                AudioManager.Instance.PlayMusic(Parameters.clipAddress, Parameters.volume, Parameters.pitch);
            }
            else
            {
                AudioManager.Instance.PlayAudio(Parameters.clipAddress, Parameters.volume, Parameters.pitch);
            }

            yield break;
        }
    }

    /// <summary>Parameters for PlaySound command.</summary>
    [Serializable]
    public struct PlaySoundParams
    {
        public string clipAddress;
        public float volume;
        public float pitch;
        public bool isMusic;
    }
}
