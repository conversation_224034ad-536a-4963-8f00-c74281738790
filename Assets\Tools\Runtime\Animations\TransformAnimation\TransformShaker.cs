using System.Collections.Generic;
using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Applies shake effects to a target transform using one or more shake presets.
    /// </summary>
    public sealed class TransformShaker : <PERSON>o<PERSON>eh<PERSON>our, ITransformPresetHandler<TransformShakePreset>
    {
        [SerializeField] private float defaultLerpSpeed = 10f;
        [SerializeField] private List<TransformShakePreset> activeShakePresets = new List<TransformShakePreset>();
        [SerializeField] private Transform target;
        [SerializeField] private TransformUpdateMode updateMode;

        private Vector3 originalPosition;
        private Quaternion originalRotation;
        private float seed;
        private float frameTime;

        /// <summary>
        /// The target transform to shake.
        /// </summary>
        public Transform Target
        {
            get => target;
            set => target = value;
        }

        /// <summary>
        /// The list of active shake presets.
        /// </summary>
        public List<TransformShakePreset> Presets => activeShakePresets;

        /// <summary>
        /// The update mode for shake processing.
        /// </summary>
        public TransformUpdateMode UpdateMode
        {
            get => updateMode;
            set => updateMode = value;
        }

        /// <inheritdoc/>
        public void AddPreset(TransformShakePreset preset)
        {
            preset.Reset();
            activeShakePresets.Add(preset);
        }

        /// <inheritdoc/>
        public void RemovePreset(TransformShakePreset preset)
        {
            preset.Reset();
            activeShakePresets.RemoveAll(x => x.name == preset.name || x == preset);
        }

        /// <inheritdoc/>
        public void RemoveAllPresets()
        {
            foreach (var preset in activeShakePresets)
            {
                preset.Reset();
            }
            activeShakePresets.Clear();
        }

        private void OnEnable()
        {
            if (target == null) return;
            originalPosition = target.localPosition;
            originalRotation = target.localRotation;
            seed = Random.value * 1000f;
        }

        private void LateUpdate()
        {
            if (updateMode == TransformUpdateMode.LateUpdate)
            {
                frameTime = Time.deltaTime;
                Process();
            }
        }

        private void Update()
        {
            if (updateMode == TransformUpdateMode.Update)
            {
                frameTime = Time.deltaTime;
                Process();
            }
        }

        private void FixedUpdate()
        {
            if (updateMode == TransformUpdateMode.FixedUpdate)
            {
                frameTime = Time.fixedDeltaTime;
                Process();
            }
        }

        private void Process()
        {
            if (target == null) return;
            if (activeShakePresets.Count > 0)
            {
                Vector3 totalPositionOffset = Vector3.zero;
                Vector3 totalRotationOffset = Vector3.zero;

                for (int i = activeShakePresets.Count - 1; i >= 0; i--)
                {
                    var shakePreset = activeShakePresets[i];
                    if (!shakePreset.infiniteDuration)
                    {
                        shakePreset.currentDuration -= frameTime;
                        if (shakePreset.currentDuration <= 0)
                        {
                            activeShakePresets.RemoveAt(i);
                            continue;
                        }
                    }

                    float currentIntensity = shakePreset.currentIntensity;
                    if (!shakePreset.infiniteDuration)
                    {
                        float normalizedTime = shakePreset.currentDuration / shakePreset.duration;
                        currentIntensity *= shakePreset.intensityCurve.Evaluate(normalizedTime);
                    }

                    Vector3 targetPositionOffset = CalculatePositionOffset(shakePreset, currentIntensity);
                    Vector3 targetRotationOffset = CalculateRotationOffset(shakePreset, currentIntensity);

                    shakePreset.currentPositionOffset = Vector3.Lerp(
                        shakePreset.currentPositionOffset, targetPositionOffset, frameTime * shakePreset.positionLerpSpeed);
                    shakePreset.currentRotationOffset = Vector3.Lerp(
                        shakePreset.currentRotationOffset, targetRotationOffset, frameTime * shakePreset.rotationLerpSpeed);

                    totalPositionOffset += Vector3.Scale(shakePreset.currentPositionOffset, shakePreset.positionInfluence);
                    totalRotationOffset += Vector3.Scale(shakePreset.currentRotationOffset, shakePreset.rotationInfluence);
                }

                target.localPosition = originalPosition + totalPositionOffset;
                target.localRotation = originalRotation * Quaternion.Euler(totalRotationOffset);
            }
            else
            {
                target.localPosition = Vector3.Lerp(target.localPosition, originalPosition, frameTime * defaultLerpSpeed);
                target.localRotation = Quaternion.Slerp(target.localRotation, originalRotation, frameTime * defaultLerpSpeed);
            }
        }

        private Vector3 CalculatePositionOffset(TransformShakePreset shakePreset, float intensity)
        {
            Vector3 offset = Vector3.zero;
            float time = Time.time * shakePreset.currentFrequency;
            for (int i = 0; i < shakePreset.octaves; i++)
            {
                float scale = 1f / Mathf.Pow(shakePreset.octaveScale, i);
                offset.x += (Mathf.PerlinNoise(time * scale + seed, 0f) * 2f - 1f) * intensity * scale;
                offset.y += (Mathf.PerlinNoise(0f, time * scale + seed) * 2f - 1f) * intensity * scale;
                offset.z += (Mathf.PerlinNoise(time * scale + seed, time * scale + seed) * 2f - 1f) * intensity * scale;
                intensity *= shakePreset.octaveMultiplier;
            }
            return offset;
        }

        private Vector3 CalculateRotationOffset(TransformShakePreset shakePreset, float intensity)
        {
            Vector3 offset = Vector3.zero;
            float time = Time.time * shakePreset.currentFrequency;
            for (int i = 0; i < shakePreset.octaves; i++)
            {
                float scale = 1f / Mathf.Pow(shakePreset.octaveScale, i);
                offset.x += (Mathf.PerlinNoise(time * scale + seed + 1000f, 0f) * 2f - 1f) * intensity * scale;
                offset.y += (Mathf.PerlinNoise(0f, time * scale + seed + 2000f) * 2f - 1f) * intensity * scale;
                offset.z += (Mathf.PerlinNoise(time * scale + seed + 3000f, time * scale + seed + 4000f) * 2f - 1f) * intensity * scale;
                intensity *= shakePreset.octaveMultiplier;
            }
            return offset;
        }
    }
}